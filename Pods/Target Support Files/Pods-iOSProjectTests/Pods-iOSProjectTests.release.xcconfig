CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCommon" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationUICalling" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCore" "${PODS_CONFIGURATION_BUILD_DIR}/MicrosoftFluentUI" "${PODS_CONFIGURATION_BUILD_DIR}/SwipeActions" "${PODS_ROOT}/AzureCommunicationCalling" "${PODS_XCFRAMEWORKS_BUILD_DIR}/AzureCommunicationCalling"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCommon/AzureCommunicationCommon.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationUICalling/AzureCommunicationUICalling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCore/AzureCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MicrosoftFluentUI/FluentUI.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwipeActions/SwipeActions.framework/Headers"
OTHER_LDFLAGS = $(inherited) -framework "AzureCommunicationCalling" -framework "AzureCommunicationCommon" -framework "AzureCommunicationUICalling" -framework "AzureCore" -framework "FluentUI" -framework "SwipeActions"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
