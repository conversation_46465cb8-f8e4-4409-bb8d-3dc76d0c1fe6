ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCommon" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationUICalling" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCore" "${PODS_CONFIGURATION_BUILD_DIR}/MicrosoftFluentUI" "${PODS_CONFIGURATION_BUILD_DIR}/SwipeActions" "${PODS_ROOT}/AzureCommunicationCalling" "${PODS_XCFRAMEWORKS_BUILD_DIR}/AzureCommunicationCalling"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCommon/AzureCommunicationCommon.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationUICalling/AzureCommunicationUICalling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AzureCore/AzureCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MicrosoftFluentUI/FluentUI.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwipeActions/SwipeActions.framework/Headers"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_LDFLAGS = $(inherited) -framework "AzureCommunicationCalling" -framework "AzureCommunicationCommon" -framework "AzureCommunicationUICalling" -framework "AzureCore" -framework "FluentUI" -framework "SwipeActions"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCalling" "-F${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationCommon" "-F${PODS_CONFIGURATION_BUILD_DIR}/AzureCommunicationUICalling" "-F${PODS_CONFIGURATION_BUILD_DIR}/AzureCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/MicrosoftFluentUI" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwipeActions"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
