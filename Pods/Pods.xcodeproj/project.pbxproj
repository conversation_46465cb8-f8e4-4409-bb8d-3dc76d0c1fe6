// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXAggregateTarget section */
		17D2EB69FA839BA1C46085F1047783CB /* AzureCommunicationCalling */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 9B8484DC33D295C398D28B22A2965560 /* Build configuration list for PBXAggregateTarget "AzureCommunicationCalling" */;
			buildPhases = (
				4B6724BB9F2E6B8B69BA870D6D2EF397 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				A5F48A46EAE76721109F60E62097EE8E /* PBXTargetDependency */,
			);
			name = AzureCommunicationCalling;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		00012CD2197E36340938B07EEF666D4D /* CallingState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58732D1B29C41808E4C37629F8FBAFB0 /* CallingState.swift */; };
		004CE727ED816CEE02AA58E4106C33D2 /* FontExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6876A1C4E1E76EBF351C36543892A55 /* FontExtension.swift */; };
		004FF5F360B5F0E6B7B3FCD710759D69 /* StaticTokenCredential.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56421BBE1E308F6C975EF9D76BE8B499 /* StaticTokenCredential.swift */; };
		00996F78C630F3DD14B79F5B2D873F41 /* RemoteParticipantsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6B1DF1EF3DB9D83F0114F0D5CA7D7E97 /* RemoteParticipantsManager.swift */; };
		01BFFEDD13236D7BC8F6F600218D8813 /* ErrorInfoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33DB302B50426EF421D2F53601912405 /* ErrorInfoViewModel.swift */; };
		040F0525EF382FE5A713A14B5FB5D29F /* RemoteOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 357A6C7BC1E1B1456C3B944CC0AA7556 /* RemoteOptions.swift */; };
		041BB5E46C732DF4C216271F8FC669A8 /* CaptionsRttInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9347FFA384836ED2CFC477F03046E68 /* CaptionsRttInfoView.swift */; };
		048C6A7D070384B9D1792A505C6DC75F /* MeasureSizeModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F722929799BB6727D9F120813E18DC5 /* MeasureSizeModifier.swift */; };
		04C3F3AA550F744E5749E064B861CE86 /* ReachabilityManagerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 801D77A0AAC9D93241C56A9E090A21CA /* ReachabilityManagerType.swift */; };
		05780F596DAC4D3D70BA4F462DCA9534 /* DeviceProviders.swift in Sources */ = {isa = PBXBuildFile; fileRef = B375851E21FB2D512DBBC63F1B045EC8 /* DeviceProviders.swift */; };
		05925019960A2540DB24837EDA6F7C34 /* MSFAvatar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3992DC8F2E3FA6345E28F02D0C9B4979 /* MSFAvatar.swift */; };
		067AEEC893BBB76B28A40822992906F5 /* AzureCommunicationUICalling-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C9D31FF9ADCCA290A6255170F20A809 /* AzureCommunicationUICalling-dummy.m */; };
		06B1B19313A0BF97FF2032EFC199226C /* Pods-iOSProject-iOSProjectUITests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C06ECB67B0B0C5AA3FC10215D17D7D2 /* Pods-iOSProject-iOSProjectUITests-dummy.m */; };
		076CA0918153BC13A046DCFD3D9365A7 /* NavigationRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC75BD097424C53906FABBAE47E3893A /* NavigationRouter.swift */; };
		07A7D49DAF1A09444CD31393AA8912B6 /* CaptionsErrorViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA5065ECA2F7D34771CAE78F77D368DD /* CaptionsErrorViewModel.swift */; };
		07CE389708067C49A4755F8736B79DFC /* UIViewControllerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CEF10B711B3B51BCCD4B22AD92714D6 /* UIViewControllerExtension.swift */; };
		08F254A7D274B005A2855485EE013861 /* DeviceExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A52F633C7D0F66B3802222AECCF3651 /* DeviceExtension.swift */; };
		092C86B4BC6EBA14881BF268452B1F18 /* FluentUIHostingController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A28779FEA75D186EF896EF16A8ECC60 /* FluentUIHostingController.swift */; };
		09850CAC348521846D7CA3009F2C504B /* DraggableLocalVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 193F59C267BDC2954DCBD29D8FDDD40F /* DraggableLocalVideoView.swift */; };
		099445C2F8FF77EF4BE5B100E5A6248A /* ParticipantGridCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFFA8F5146A63328959236141F803FCA /* ParticipantGridCellView.swift */; };
		0A6A4EA5FBE8688A888B65481113EE1B /* Collections.swift in Sources */ = {isa = PBXBuildFile; fileRef = D711CA6E12B3027CF883DACF065B98C2 /* Collections.swift */; };
		0A732E9997B69403EFCAD1FA51C5CAB7 /* CallCompositeCaptionsData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B5BEFFA3B0DA6976BC05D13CB8AAA62 /* CallCompositeCaptionsData.swift */; };
		0AD2FF727D9AFE18C187EC5D90CBB5DE /* AppLifeCycleState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 42318D37CA282796B604ABA8F9095C0C /* AppLifeCycleState.swift */; };
		0AEAB5AFFE8E2B4747C64CC205B6D952 /* AvatarTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BBFD847342BE87933D544A2FFD66F91 /* AvatarTokenSet.swift */; };
		0B1CCB7A086A3840223C533CFA7E1DD6 /* ACSCallingStateExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = D61B29536A94347135E1D3D9762271EF /* ACSCallingStateExtension.swift */; };
		0B5D4A5323010339A3469A65567F028F /* SwipeActions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C084400BE0A523B80129C509BBD7C214 /* SwipeActions.swift */; };
		0B66BC11A8E5A7090EDE33E2E6B68EF6 /* CallingReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EDFA7A885D3AE403DF6AAD89DC57623 /* CallingReducer.swift */; };
		0B9CF34B3EF3EF41420F01D0FC7B2EE6 /* BlurringView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F2023AB9DE89186B164BB61E5FF6F424 /* BlurringView.swift */; };
		0BEC1974A593B70A5BEC52B86C2793D0 /* VisibilityAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18659C0FC416310F1B04064FAFA4A419 /* VisibilityAction.swift */; };
		0D13C3A9C668A4AD69DE87675EC4BAF0 /* AppStateReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D0B3E335A380AA87D4AC3FB862125C6 /* AppStateReducer.swift */; };
		0DB0468B9EB0B3151B19E70818C724CE /* HTTPResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C7C977DC9B706F740B9806B6DA88FD /* HTTPResponse.swift */; };
		0E4EC21C8F5799022759D47670B0B3C3 /* CallingSDKEventsHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 70C47B69E283EEB360F4B72D1B78E09B /* CallingSDKEventsHandler.swift */; };
		0E9A2C6304CD48CC78854CA1A6FF9B8A /* SetupControlBarViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58AE038C337B203EB262A041A02C5534 /* SetupControlBarViewModel.swift */; };
		0FB150AEBDD3215124F8049671FA51C5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		10B343AF588E274CEC00E8C2864BAFCB /* CaptionsLanguageListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C0AAB679727109EC948F9C34DF367DF /* CaptionsLanguageListViewModel.swift */; };
		10DBC56710C46D54C27592E765AECBD9 /* LifecycleAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6135E9D116F377CA1936C39AC919E014 /* LifecycleAction.swift */; };
		112F36430BF331F07FA45D925AF534EA /* AzureCore-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5872E993C44A65010BE1315D3960CF5A /* AzureCore-dummy.m */; };
		11C91CD2849745D44025A98C81764FDE /* ParticipantGridCellViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C94F2B36E6A2FDCFE522B3E6B2D4238 /* ParticipantGridCellViewModel.swift */; };
		123E77C020BC8E8ACFD716BECF95C28E /* LocalOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C92E0C6D24CD0990BDFDB37080FFC7AC /* LocalOptions.swift */; };
		151DF570DE4E6AB1BE2DE55DFC9348DC /* TouchForwardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA610DA54C5958CA95C8A662D63B47CE /* TouchForwardingView.swift */; };
		15D69772913E1C8DE4A9B0627154FC38 /* TokenizedControlView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59B48080D1EFD51F65E3049A052FA1FB /* TokenizedControlView.swift */; };
		16503E84B9FDF9BB5BA82EF5FB2856FC /* IconWithLabelButtonViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7858A523D8CE7E9B32B3AD2E0FF1277 /* IconWithLabelButtonViewModel.swift */; };
		1802CF0C0C13533FB3BB8DEF72B99E88 /* AzureCommunicationCommon-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = DEA207A747DFC202BB7FAF307570B629 /* AzureCommunicationCommon-dummy.m */; };
		183E28251C071CABE2E513570AD1A925 /* CallInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0080BC482A77463BAA32B97A9315BDDC /* CallInfoModel.swift */; };
		18AAA34A116A93EAAAB05AAA1EF94DE5 /* PlatformInfoProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* PlatformInfoProvider.swift */; };
		18D633BAFB9AD09787C8D0C2EC6C5EF6 /* PermissionState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5FE547F9EF02B7E6F687256F19E8E695 /* PermissionState.swift */; };
		19B507AC3EAB7002E57C7CCEA32AB907 /* TokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 50254538F3951872CDAFF851D017119D /* TokenSet.swift */; };
		1A3BA465AF7F75D84AAFE5C4DEEF2092 /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738B9D5CFD1A8F6FB96730CB4C78AC83 /* String+Extension.swift */; };
		1B56FA5933883FF4166964433101EBE1 /* CallCompositeInternalError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DFFCEB2678C46FEB76ECA7B86C58867 /* CallCompositeInternalError.swift */; };
		1C99B416317923E81E3E610561122264 /* ContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF8CA1902A3E746395C999BCE8E1AB5A /* ContainerView.swift */; };
		1D030B97AE71B6D3D8A3CD0B4B8BF4C6 /* ActivityIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = E6BD320AE43B783EDEBFBFD37F071226 /* ActivityIndicator.swift */; };
		1D110AB30DB7D87E42D85EB1FF85FFCD /* ParticipantRoleEnum.swift in Sources */ = {isa = PBXBuildFile; fileRef = B77633AC8AEBFB5CF5825354030B78F9 /* ParticipantRoleEnum.swift */; };
		1D80851A8E6E50AF65CBE60A0BFF1B59 /* CallScreenControlBarOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = B296179CD125E8BBFABA7FAFB11DFC9D /* CallScreenControlBarOptions.swift */; };
		1E6505CA48D7054A30D571EB83217FEF /* JoiningCallActivityViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59F3D5210DA73FA0305605C65598A66C /* JoiningCallActivityViewModel.swift */; };
		1E6DD61D41F5D325E140B4977D6AE1DC /* SetupViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5ECE18B19EFA2A761E3CB028CE0B500C /* SetupViewModel.swift */; };
		1EE71CFAA4B5619E7FFCE6C46C8B7EE3 /* NSLayoutConstraint+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3891CEEBB5A4B8FFB9E0FA827E01AAAA /* NSLayoutConstraint+Extensions.swift */; };
		1F786B1750D4FB32BA78A1BA1B101522 /* Middleware.swift in Sources */ = {isa = PBXBuildFile; fileRef = 068739BFD2E8849C70391A0DE1E27E17 /* Middleware.swift */; };
		1F9BF4E6FB5B62AE01ADE2244CB3B2A9 /* PushNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36029DB0C38EEBA6052EB14C8E6E52BE /* PushNotification.swift */; };
		20551876F01A8BE2F428DD77CF4C235F /* SharingActivityView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1A0C3BC83D750EC2B24F0A4B63CA13F /* SharingActivityView.swift */; };
		219AEF40B7FB8E57A57160502455E945 /* StringUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2FE143DAF7581E7C92674E75F7017A6A /* StringUtil.swift */; };
		22CC8B323CB73C6F5DFEA4C915A89844 /* PermissionAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12C368BF65434904B90FCAF82BA91ABC /* PermissionAction.swift */; };
		23C0EC078926329B57D985B49295B46D /* CircleCutout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 177F54C04B7D49F51BF95447C8F0730E /* CircleCutout.swift */; };
		240E69B9C94317361259CD45DFB76A3C /* SourceViewSpace.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3206EBFBBC3FB28B0166ACBBCCE1394 /* SourceViewSpace.swift */; };
		24574998DDC0C8ABD9B2B4357C5E23B0 /* RttReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 89E6D13DD0567DF3585578CE6EBEB112 /* RttReducer.swift */; };
		24AAFE493727F70A1E1006F9B3976087 /* CustomButtonViewData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0841F2E163FBD3258C7CFEA3CF3FBBE3 /* CustomButtonViewData.swift */; };
		24E3EE0FA0254B5D383A63C220AA8C1C /* DrawerGenericItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2117D4B5A8620CB0095849CCB6E80C47 /* DrawerGenericItemView.swift */; };
		2567496D306EB1ED2897F7D9AF5F6828 /* NormalizeETagPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9404205354FF9A7223C96D0934A51ED7 /* NormalizeETagPolicy.swift */; };
		25CF662835E7D3F6B0D9E63BD9CE56A0 /* PopupMenuController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59146327A33EEE4EC4A144BB2D95B04D /* PopupMenuController.swift */; };
		25EAE6DDAAB60EA189D93197AE909384 /* IncomingCallCancelled.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A9A6D03F74274EE6AEDBF0CB25CA7A9 /* IncomingCallCancelled.swift */; };
		261B901F2D412AEB750635C17464AD1A /* IconButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F095010ACD14AEE4E2B4CD0EF4379B4 /* IconButton.swift */; };
		2750BEC57D0D4BA8F5F9C21952E2D594 /* ButtonTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = B22C9C500FE4BDFC523CD4534BFCC59A /* ButtonTokenSet.swift */; };
		2761FE4D12BE1148B6D6EDE368275629 /* UIColor+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 60E48F5F2991D7AFAD34036D67BC593B /* UIColor+Extensions.swift */; };
		277B118EB3AFE1F1D92DC9EDD31A6E35 /* JoiningCallActivityView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90D50155493B61A0569090870A2135DC /* JoiningCallActivityView.swift */; };
		2790630686192C7117AA00EC7282A33A /* PushNotificationEventType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35EB4DF2FFAC5624054E8EFC279C9DAA /* PushNotificationEventType.swift */; };
		27FEEAAADEA518362EAD341AF6B784F2 /* DebugInfoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 544338A618970DEA569F4C6A0A511B33 /* DebugInfoManager.swift */; };
		282F5EE7D90BEF0B025F068DBB0A1098 /* ColorThemeProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6531966EFBFDC7768369081A621DE586 /* ColorThemeProvider.swift */; };
		2858FECC7E6BC63E54F4CBEC268C30B1 /* FluentUI-apple.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9F477F0DADAEEDC6A0C736D725A8FE47 /* FluentUI-apple.xcassets */; };
		286D7E546FD42753E04E83F843E6FD15 /* CallKitRemoteInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = E27C775AE1409D946C84E0E5A6090D9B /* CallKitRemoteInfo.swift */; };
		28867EDBF3E70B3AD454C260AF0D079C /* DrawerViewControllerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5DD289A3EB5470C361F1E553F4B91AD3 /* DrawerViewControllerProtocol.swift */; };
		29056FA748ED2B508C524B1EDCF38B68 /* SwiftUI+ViewPresentation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EC41BB5A297CCB29ABF141D36B5D67A /* SwiftUI+ViewPresentation.swift */; };
		29803D4B7E0A813A4DF722ED07C727B7 /* RemoteParticipantsReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 69F87B3A7FD09BB690412D6EC10FF1C8 /* RemoteParticipantsReducer.swift */; };
		29C93E51E27245645CB5E92BC468BD32 /* UIApplication+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 86A6D8888B34A734A9E122615D138F72 /* UIApplication+Extensions.swift */; };
		2A9899A66AC14AC9024E4189D3A0E261 /* AccessibilityProviderNotificationsObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71F4AAB750FB164B117EFD1F78D4AA9D /* AccessibilityProviderNotificationsObserver.swift */; };
		2B10BEFAF62BCBDDBA6F52A8078A741E /* ThreadSafeRefreshableAccessTokenCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC46B7DA2536B8F8F3B00D521A8E3E6 /* ThreadSafeRefreshableAccessTokenCache.swift */; };
		2F823F1277078274E855F71620E159C3 /* ParticipantGridCellVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 470E3141B7AF49108835912477BE1C6C /* ParticipantGridCellVideoView.swift */; };
		307390689B73FF9E9242906D3B122B4F /* Persona.swift in Sources */ = {isa = PBXBuildFile; fileRef = 421BAA4C8B9F1E47B66491B84536D33D /* Persona.swift */; };
		308CF2BD47568B9EFDB000F02DD6D9AD /* ClientLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9D91EEAA0D57984BF35581E10E1148C /* ClientLogger.swift */; };
		31772EA29D3158C3730C1F49A66352D3 /* MicrosoftFluentUI-FluentUIResources-ios in Resources */ = {isa = PBXBuildFile; fileRef = 419FF83B5DE08258908F2BF54EA6B771 /* MicrosoftFluentUI-FluentUIResources-ios */; };
		31A77B5C55CF55A10AB0099541C437D3 /* ShadowInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8258DFFB1E94835CC3E87FE47CC21530 /* ShadowInfo.swift */; };
		31DCC2BA6330AEE0C9DF122473076D28 /* MSFActivityIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A31114F90D8DAD40C5677C1B8BC5DE4B /* MSFActivityIndicator.swift */; };
		32BD7F84FAEB49CB7B7212F00A39FF48 /* Icon.swift in Sources */ = {isa = PBXBuildFile; fileRef = 041A9546BC831DD4903B886FB8B28807 /* Icon.swift */; };
		3380FBAF65E543FA2E5E7A4DF5AD26A3 /* ControlBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A563B17F12AA0B0DC4D1E5C448A1236 /* ControlBarView.swift */; };
		35B5229C8B752858CF0F7AD01F409228 /* CapabilitiesManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E6E68698D479A4417ACEF986F8120CB /* CapabilitiesManager.swift */; };
		35B894D33F0A632AE1164B90BD06EAA8 /* Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBFB4FCCACDFCE7FCF5EB367D3A30495 /* Fonts.swift */; };
		36F09F2364AC6A5C0596F9DAD00AE9FB /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		37FB31BBE45C81B56D802BFC09368B3C /* ToastNotificationAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98628DE2E3D3A0E442AA61B799429A8D /* ToastNotificationAction.swift */; };
		380FF7B3BDA4BAAD3A21945BA36F35DB /* ConvertingInitializers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62755A819E1320B8A63CEA61C0EC9153 /* ConvertingInitializers.swift */; };
		38F8179AF90370C251183A999E7D7609 /* CustomAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B6353F50701D842A002FF9B8E859D1B /* CustomAlert.swift */; };
		390CE79BE2A8E2DC9149B6255C5F05CE /* CapabilitiesChangedEvent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49C58831712EB325A9DB3AEA7E2CC6BA /* CapabilitiesChangedEvent.swift */; };
		39B40F49FF5A216729AF58F0B7927CD2 /* CallDurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1203542E28AB32D844C7AA6879500F4E /* CallDurationManager.swift */; };
		3A3B2D9563B174579E9AC6737A5BAE7D /* PreferenceKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6860143A03DE7807AF0E4BC47E73FA00 /* PreferenceKey.swift */; };
		3B518F3883FEA0D80995F528F210316F /* CallingMiddleware.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99330688314D8B66BD7D2087C17C1B3E /* CallingMiddleware.swift */; };
		3BDA3BC3B55B0AE7E660D70D48780859 /* CaptionsReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 29B80483762E8C6394430ECED0E38DAD /* CaptionsReducer.swift */; };
		3BE786D7A57152648E67CD0D74EFD016 /* DrawerViewModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 027F46675C7F287E56408C08C15C73D5 /* DrawerViewModels.swift */; };
		3C1B96E5466C7B538455E1BE278DF239 /* NotificationCenterName.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF49763E8F89C09177863A3ABAAA9958 /* NotificationCenterName.swift */; };
		3C9D912227D956E95EFE31594E486D70 /* SwipeActions-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D1AF8F21737D67ACC40F01E672C0416B /* SwipeActions-dummy.m */; };
		3CE0B54BC9C2BF95C3AF25C3B771B978 /* JwtTokenParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 43B49E5D2505CBF81E0779096790AE01 /* JwtTokenParser.swift */; };
		3CE87FF273AE3865D5D04916EE674C9A /* Pods-iOSProjectTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C399A358BC2C490D90AC384CADCDCD7E /* Pods-iOSProjectTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3E5039BD48BF93C5ADCE6FC020494974 /* ThemeOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4F88B9F46FDE2365645E2076AA1AC802 /* ThemeOptions.swift */; };
		3EE4DD341FBDDEC908FABD169FDEBD9B /* ButtonViewDataAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE3643C6FA971B88D53BEC7C54D9A909 /* ButtonViewDataAction.swift */; };
		3F0318C7D7A0CFD151F4D6D8FE99F518 /* UserAgentPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFAB7AA29AB873290F2BB6A0CBA8E41C /* UserAgentPolicy.swift */; };
		3F701639A93AB84D96849AE385360BD6 /* ViewExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9DFBD9606077F980791A7E453C7B80C2 /* ViewExtension.swift */; };
		402A9A306612F515058D33C3F12B3BB0 /* ToastNotificationReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A58FA4FEE4F8BC380EFA302A5425FBA6 /* ToastNotificationReducer.swift */; };
		40CAEC14F66158653A0F4462D95E597B /* XMLMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB9E810146915DE74F09C23CFF866458 /* XMLMap.swift */; };
		40E234CF438CE3B2063F8A25C021B4CB /* MSFAvatarGroup.swift in Sources */ = {isa = PBXBuildFile; fileRef = FE4CEA862D8B884C3F02E92096642407 /* MSFAvatarGroup.swift */; };
		41291732F94EAD1160C25AB02D3380B3 /* ApplicationUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7A1EF53D3DD231F158CD09CAE20A135 /* ApplicationUtil.swift */; };
		434D6AF19EB3F8304417BCDDEE967E21 /* IncomingCall.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38A559F770896C057085120739844D3E /* IncomingCall.swift */; };
		44B5FA5A42FCFBF6331CED0A5EE79D11 /* Avatar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A805D016E85F90354680EFC21A160FF /* Avatar.swift */; };
		45CF83A1205B7D85C0336FF053933427 /* DefaultUserState.swift in Sources */ = {isa = PBXBuildFile; fileRef = D000FFF6214A0D59E84174F78D66A4A5 /* DefaultUserState.swift */; };
		4664F6574FDCB8680BF6339EB71B3AA0 /* LocalVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 69A1C089943204C4AE2DAC38A62D12AA /* LocalVideoView.swift */; };
		4759AC8A4ECD58B50CD4962EE42E58B2 /* InfoHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7724B3604CF20668E6B53313DDC88CF3 /* InfoHeaderView.swift */; };
		48C77443C2E3653B173F82EE78F943F8 /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = B772B257C1BF436544C8E9075FE443AA /* AppState.swift */; };
		48D389E273B5EE62089E2A8D6DFF168E /* RttState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B251147CB69E271004FEC65D997D86F /* RttState.swift */; };
		4925C496FE4CFE65DBB0DB5ECE41E7C1 /* CallScreenInfoHeaderState.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA78D346CFEABC00DADEB7E40F34EAFF /* CallScreenInfoHeaderState.swift */; };
		4949D2A7527FE086E1EF0045E4B4452E /* CommunicationTokenRefreshOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 989E4D12CE4C5DAD41D76D4F9C90111B /* CommunicationTokenRefreshOptions.swift */; };
		49D429D5DCA89BDBB3D75C83D928DB5C /* CallCompositeError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4E95D969C014886926D36D05A1028A7D /* CallCompositeError.swift */; };
		49E1E62AA577678C688DFE60CA4331A8 /* CompositeExitManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EEC83AA5B34B5CA3012B756A68737FB /* CompositeExitManager.swift */; };
		4A60E019D322B6FF680B16AB2BEB9D10 /* IconButtonViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5F9E74FAE941FA03F3D71F803B2F2551 /* IconButtonViewModel.swift */; };
		4A9B9408FAC3205F764A49BBC54B51E6 /* CallComposite.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B096016243FC8A40EFCC8FDA5079DD5 /* CallComposite.swift */; };
		4B5A832591B0DB70CA4250DAB720EA88 /* StyleProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = A604BE74E03E15058F25D4F54BCE28C7 /* StyleProvider.swift */; };
		4BA976F0289433E00AA78830CCB4D097 /* Pods-iOSProject-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 713AE5DD6CF53E0A12CA3B0630A402E7 /* Pods-iOSProject-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4BEF9E8AA54A04150DEC21FD071D3454 /* CallingAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 70F84D3362118FDE5C4F28877FDF3ADB /* CallingAction.swift */; };
		4C7AB95FA66D689618108EA18A1B47C4 /* BundleInfoProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 72277D2220FD0CC7547C22183F0C6EDF /* BundleInfoProvider.swift */; };
		4D7094ED0D7529834E916A5C9F369F1A /* CallError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D1C4DCEE12252D7285D284F6FD69114 /* CallError.swift */; };
		4D89B0D8BFBA730FD62EAFB1EA2A6B94 /* DebugInfoSharingActivityViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C6FA52F3EA47D073C9052E26172911 /* DebugInfoSharingActivityViewModel.swift */; };
		4E3D31E6D7749E2BAFBA13D2A4D81195 /* XMLModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E404C34A082E5C697545707969AEFBE8 /* XMLModel.swift */; };
		4F90F1D4138F494805BCBA72BAB79465 /* PipReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 684829E58B8B328DFBB2C1CB8B1BC2E6 /* PipReducer.swift */; };
		5192768BF76D3ADB9348068020DA005B /* LocalVideoStreamExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5CD6AE56E1F1FEA7A07966D3AC952D93 /* LocalVideoStreamExtension.swift */; };
		51A19204A204FAC3B95FB2D91E216209 /* BadgeLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B0F320A2591C6C10D912F67D2A54F374 /* BadgeLabel.swift */; };
		521D2DF708EAB3095E244564D71EE4AE /* LocalVideoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66B83E0A984344AF9E317FECA71A1DEC /* LocalVideoViewModel.swift */; };
		531FB05655363F9AD0889C66823CCD5D /* MessageBarDiagnosticView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E9435372F287B5673FBBAB771DEB5B1 /* MessageBarDiagnosticView.swift */; };
		532A84077BA89E66B64FA7DCC9CEE47A /* IconAndLabelConversion.swift in Sources */ = {isa = PBXBuildFile; fileRef = FBD6D72FCC3C0A4D97D30AA94B92A02F /* IconAndLabelConversion.swift */; };
		5354F1537FBE7D5F007D01F63109AA8D /* MappedSequence.swift in Sources */ = {isa = PBXBuildFile; fileRef = 72E11C4258EA6DFAB8A1CC19A677796A /* MappedSequence.swift */; };
		538465E79FFAB7B7451AF822B06DC4C6 /* CALayer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE65509D035ACE1E4A3C088E261B1328 /* CALayer+Extensions.swift */; };
		550C27E668AC6B100264F97CA79B97DC /* HTTPRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7C6D6EB5615EE642CB6AD7F12949928 /* HTTPRequest.swift */; };
		560AC5C1C1F25333522663FB68292D0A /* CallHistoryRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = F65DFAB111B4D66F81FD48CF18BEEFDA /* CallHistoryRepository.swift */; };
		5721DD8ECDF5397303D9ECB10968E9E0 /* IconWithLabelButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAE8250C171D3BA639B7D7B97F47011 /* IconWithLabelButton.swift */; };
		572ADE6C6BFE0D50BF5623E987442433 /* CallCompositeUserReportedError.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB57C5C0EC0087608DB222E64B01247B /* CallCompositeUserReportedError.swift */; };
		57564CFE260F7C36696385F1592D8E56 /* TableViewCellTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1EE3C1452BD5D657E2171DF60B8113D /* TableViewCellTokenSet.swift */; };
		576B94ABB1FDAA10B850BFA1C65F82C4 /* IconProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 063677845578DE45849A2145570283C9 /* IconProvider.swift */; };
		58E5CAE150D818EAD4828D44F29D6440 /* Label.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C2ECC3C94603626E6A959FC467460AD /* Label.swift */; };
		59A07A5D81053E47D66A75BFF6314B70 /* ControlHostingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B5B8D09A6C133E60E88624CC65F2635 /* ControlHostingView.swift */; };
		5C29138CE6075F159DC9383AEB70D30D /* VisibilityState.swift in Sources */ = {isa = PBXBuildFile; fileRef = F6F4C386E58F309333EF74C5DD9450D0 /* VisibilityState.swift */; };
		5CB783C4A87A7A86EC8C3B15E1E66EB4 /* TelemetryOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD3235C5A9DF6ABC738F712772FC895C /* TelemetryOptions.swift */; };
		5CC6CA3E0FF429B58CE4126C64EA5F5F /* RegexUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1997DC2FA21B7EFA5ACE0B182B731450 /* RegexUtil.swift */; };
		5D99C7C3C274515349C0D1F4039BB74C /* AudioSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5D00D63466046D0CC8CEC2C684AF872 /* AudioSessionManager.swift */; };
		5E693F265BE6B958A6D3F174E5C73E7D /* ParticipantMenuViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1F4895CC34CF03EFD122E0BBBF3421B /* ParticipantMenuViewModel.swift */; };
		5E79866EB517A383F5E4D1F56D2BF3F0 /* CallDiagnosticsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39095A9B9926B6694C02C86037E9A96C /* CallDiagnosticsViewModel.swift */; };
		60E3A8A423034D2BD6C3B5A50B003447 /* MoreCallOptionsListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B1E28F5E472EE59C17A2F72ADB8B8219 /* MoreCallOptionsListViewModel.swift */; };
		61765A5E5690C18B0BD10ABF32A6CFD5 /* AccessibilityProviderProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = C36108D33AF689B99BBE37A24F9A438B /* AccessibilityProviderProtocol.swift */; };
		644B57FACA69DB033834FDF726B9B512 /* Localizable.stringsdict in Resources */ = {isa = PBXBuildFile; fileRef = 7708492A1C3815979A7398F14E2015BD /* Localizable.stringsdict */; };
		64A0DDFBFEE93D7B864CF14B341D9ED7 /* ACSDiagnosticQualityExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = D51B69EE13AFC7B1105AED2DE0003747 /* ACSDiagnosticQualityExtension.swift */; };
		64BEC5E162038C63083E84EF369E2120 /* ColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2E998DD66E9652A98A669608F1AE986 /* ColorExtension.swift */; };
		64DF86492B045AF89441EEFF6EED5456 /* ThemeColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55E6A5A541EDA812FA06386F0549BB33 /* ThemeColor.swift */; };
		65B23BFC83E0B6338B8BCAD2BF7E17D7 /* CommunicationCloudEnvironment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32E5AB2D1D4125269E7EFD3A59E27FF3 /* CommunicationCloudEnvironment.swift */; };
		65B980A2D63FB90664EC77873C7014FB /* URLHTTPResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEF4C977599D76381B061565A7F68DD1 /* URLHTTPResponse.swift */; };
		667B68B7D87EE6AE155209E6ED3E291D /* UserFacingDiagnosticModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4E281BD3A0DD63139190CA6BD921C536 /* UserFacingDiagnosticModel.swift */; };
		68E7A1D137A749BD86EFD2A659E98DBF /* MoreCallOptionsListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 16F7A9061740284EA7D9EB068AC365CB /* MoreCallOptionsListView.swift */; };
		694BCC1560EAE6810488B98087C4EDB7 /* ReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 011E504D718112F73B239A9B72981DAD /* ReachabilityManager.swift */; };
		6998867E4319B98E94388557B105AC1A /* LocalizationProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2902D8EE5D7ADF5746BFADA4C69D948F /* LocalizationProvider.swift */; };
		69AAD3EAE6F49B2F1649AE851C6AA781 /* Copyable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2DD50C462B2077D8009F309EC9A3DCA /* Copyable.swift */; };
		6A9F0B4EF6ACED79B6F134AA5FA2721B /* FluentTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2631D831F5001B64C9BEDD6BCE13445 /* FluentTheme.swift */; };
		6B1E0DBACB4279E74C0DF6AE3FACD05F /* CallHistoryRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 279FB395473B3C0BE404C395A17EF437 /* CallHistoryRecord.swift */; };
		6B82D93A6CC3ECEFCC23DBFDF1157F1A /* PopupMenuSectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F5552064955997B1FF9FDBD6A4B122 /* PopupMenuSectionHeaderView.swift */; };
		6D45808DBEE399B47D9672DE4E8F1912 /* SetParticipantViewDataError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04D9C539A6E4A7FF5C0C554EA23FC732 /* SetParticipantViewDataError.swift */; };
		6DE2B00D0EAC8EF34139F9ED41A27B71 /* Errors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B8E9241C0858619F232FA308E9B4BDC /* Errors.swift */; };
		6E9136211D2700BDCA1B1B13E45D3524 /* ErrorState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37B2217166ECA8A7E581A68CB86867C4 /* ErrorState.swift */; };
		6E96FDF25F812237FDAADB43AFDB0876 /* CallConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 063FADACC276E44058ACBAA52A01152E /* CallConfiguration.swift */; };
		6EC18A3FC89C994461F9D7529772E709 /* CommunicationTokenCredentialError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D5130D0FB0CEBC82210A8F669CD58F6 /* CommunicationTokenCredentialError.swift */; };
		6F6D39DF02ECF2EEEC77FFD1E43AFA1A /* AudioSessionReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF9561D60C46432EA43238A09800C510 /* AudioSessionReducer.swift */; };
		7048171C689DCF6162F5DC580B5E7DEC /* ACSCallEndReasonExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F5448B6BDB3FB53E36B8A4142376E35 /* ACSCallEndReasonExtension.swift */; };
		70497CF99ECA33D90F1EBDFF12261286 /* UIView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = B69EB277115DEA987339EBFBDBAF3B32 /* UIView+Extensions.swift */; };
		7071AC92461EE2672658C5656CD16AFF /* ParticipantListVIew.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E6942AC18DB9B758098A0C2AF30DE35 /* ParticipantListVIew.swift */; };
		715514B1FFF38115DDAA21399F965F83 /* CallScreenHeaderViewData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 40502CAA76933496B20D68093A435F06 /* CallScreenHeaderViewData.swift */; };
		7172A6F1FE804A3BE80A899EA1BEDA4E /* ContentHeightResolutionContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9B6E733557E9DB456936CBA5C401B96 /* ContentHeightResolutionContext.swift */; };
		722873FE4AF3915427CE6495CBD82756 /* AddDatePolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 91A315E48F79E42026749857DF9D40FB /* AddDatePolicy.swift */; };
		7231D1F159C9FD9E82F4351CBA93380D /* AudioSessionState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 408A3AE5333ED59ACF32EC58DBC49E03 /* AudioSessionState.swift */; };
		72B48EC500962A87FB39F0579998E2A7 /* VideoRenderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E31637A3AC15FC5289F1CE3FCE2BC7A /* VideoRenderView.swift */; };
		748C8C2FB05B5EBAA3CF040E5533A71C /* AudioDeviceType.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC96DA053E2A69F84FE5C41A5EAC1470 /* AudioDeviceType.swift */; };
		74979E999F1418843279D7A22085CC47 /* GlobalTokens.swift in Sources */ = {isa = PBXBuildFile; fileRef = EC2E73C06448CD60737C47EE44751459 /* GlobalTokens.swift */; };
		761508424E7CA85A4E6F5DA57B553284 /* MicrosoftFluentUI-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F66AA86C0D9004E91FE8FA2AAB29BED1 /* MicrosoftFluentUI-dummy.m */; };
		77BB0350BFC3FBA24D1C0A9DDB65520B /* DiagnosticConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B12CC5A0B15BCA3FED5ED7A7125AD77 /* DiagnosticConfig.swift */; };
		77FA3F4E2429E5C84EE5CFB65DF05673 /* CallPipVideoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F311DCBB6CE9BF641EB4C151B7B1789A /* CallPipVideoViewController.swift */; };
		781ABE65B78D9C1AD34ECC64350FA98C /* PopupMenuItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278EC9370F396F8BCA5ACD3787243BA1 /* PopupMenuItem.swift */; };
		7860B2E8C020DED4AC7F36619BC6747E /* ButtonState.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6E5D1979C95206398F3E37F61401690 /* ButtonState.swift */; };
		7865AA9B34316F69BCC1F993EA3B3F68 /* VideoStreamInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C92BEB6651D97E4A6A379DD09193612 /* VideoStreamInfoModel.swift */; };
		78A6DE8437CE93437B32AA7151B34E62 /* FluentUI-ios.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 488F28FA0B8F2A146DDB19B952EE5EF3 /* FluentUI-ios.xcassets */; };
		78DE72917B38EECC0B1B34A75BDCA52C /* DrawerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 873D32D3C85C8CF0AAA3E6429F065BF1 /* DrawerController.swift */; };
		791B02C083780D36A357DEB07DF117D8 /* BottomSheetPassthroughView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17D47BFC91D1F0CDA499B5A0919725E1 /* BottomSheetPassthroughView.swift */; };
		7A3255B1EDBBC760E7723A7AD487E800 /* Pods-iOSProject-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = FA20A5B2176EB4E24B773E4C3B84D377 /* Pods-iOSProject-dummy.m */; };
		7B04D407EDF5487967F996C17E3ABD6F /* ErrorInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CEF302F2865D5EF55FFCF3E9F55120F7 /* ErrorInfoView.swift */; };
		7B9CA1CA57F05AD62126C88CF7A34EC0 /* AzureTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5437291470A18CFC7EE83A02866CFAED /* AzureTask.swift */; };
		7BE4912307486F98DBE46AC05BE011B5 /* AvatarViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11B8A2F72EA9743D1744DA44D2D9B93B /* AvatarViewManager.swift */; };
		7C60E255BC3FE5BA2BD7FD4DE7B14E33 /* PermissionsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1C1AE8A53E82FBBBE179D3147C2E2C /* PermissionsManager.swift */; };
		7C70C4641AFC1991F3087C60ED1B645E /* AvatarGroupTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 68E593E829379139F55496F13110A628 /* AvatarGroupTokenSet.swift */; };
		7CA90BBDB76FDE44C868537BA2D59F44 /* OrientationOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87D3BB8D380E82C2DC20476E49EC67BA /* OrientationOptions.swift */; };
		7CC5F8AAEA1EBE60E8797C0F41F02397 /* TokenizedControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 10AC7C2113662A725FAA3E1389F6BA70 /* TokenizedControl.swift */; };
		7D75DDF706E962944A06F243864D8EDC /* CallCompositeDismissed.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9E7C2367480F88523603EDCB00C7F35 /* CallCompositeDismissed.swift */; };
		7E6E073E2EAF09F87D2B0C5AAE1E2C04 /* CallingSDKWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 75B10E1058C54DF613C799D1C9E2C464 /* CallingSDKWrapper.swift */; };
		7EB43A5B9CAA6FAC1F548165E7AF4284 /* SwiftUI+ViewAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6579F74A035C0F50EA1A33EBEFDD622 /* SwiftUI+ViewAnimation.swift */; };
		7F69E1DF8A4DE77F63F51958FB795F69 /* ZoomableVideoRenderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = EA9D5783068A0AA7BFF369C10160564C /* ZoomableVideoRenderView.swift */; };
		81334C1814E70D292CF29CD356B2D558 /* PopupMenuItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C1FA818AB4C90CA525B8452FE461345 /* PopupMenuItemCell.swift */; };
		817F00F76241C3146DF279AD0C78545C /* ParticipantCapability.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0BA07FCACB4449236F9D2748E2B97C2 /* ParticipantCapability.swift */; };
		820AC0982EA4A0C7894134EADB3C187C /* MicrosoftFluentUI-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 57531BACE40433640AE941D2E9A63956 /* MicrosoftFluentUI-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		83203FCB16FBD8EC388C802DD46A5A09 /* DrawerBodyTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE490866223E2BA6FC2DC3A2BC2D436E /* DrawerBodyTextView.swift */; };
		8466507A09421C86962E2FFD74D77206 /* ACSParticipantStateExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 269F416E43BE05BE3AEB15562335209C /* ACSParticipantStateExtension.swift */; };
		850EA74D562CB4D4178C04EEAE013741 /* CompositeViewModelFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B0BF5CF531B79E2A1F160893249238E /* CompositeViewModelFactory.swift */; };
		864F7C53F93A9EBCE4D674C9229F526A /* CaptionsRttListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD7CCB5B049008FD2F13E68BF4665117 /* CaptionsRttListView.swift */; };
		8680E20F4148F094991CE349681C2404 /* DynamicColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = DCF47F8FA0E92D20B30CBD4EE048ACD5 /* DynamicColor.swift */; };
		86B26E75590E1AECFB889D1561DF7E5E /* AzureCodable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C31F64E6A2412601E3768516A40C307 /* AzureCodable.swift */; };
		870C6688B04EE42EDEA519B8F5FF1486 /* BannerTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13A1DAA31EA4071EE5EE8A87D8065E33 /* BannerTextView.swift */; };
		871AE75220C8F916B9D30CBCB087F80B /* ACSCameraFacingExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A71F69DDEA462A85DE3C9B58C0693ED /* ACSCameraFacingExtension.swift */; };
		875DB9C1D0A956341CD747BDABDB4F3D /* SetupControlBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B258201F04C519FB5DAE449AC0F41F17 /* SetupControlBarView.swift */; };
		893C1CE5FA620D47BA3899BCD597C8F1 /* BottomDrawer.swift in Sources */ = {isa = PBXBuildFile; fileRef = CAFC82777C625A58E3DE6A302530E06B /* BottomDrawer.swift */; };
		896FD6EEE6CD1F14D3205421317B8DD2 /* RequestString.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B8A85DC1E10B66D2B686119035414AE /* RequestString.swift */; };
		8AA93ECC78A5F7257F16581A130AB79C /* LocalUserReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4087E556F3E09E569E812BC87B799984 /* LocalUserReducer.swift */; };
		8ACD6FFF4F04ECB0A55AEA6767C37787 /* TableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4EC4254B28BB6C1CF9A2A313350DD2F3 /* TableViewCell.swift */; };
		8B3E77B55CD3B6D4C66178F193A88A4F /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		8B7414A2FCC65292E51DD1C7AE176E61 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 102608995FA3ECFBF65D8E15E4ABFD07 /* Assets.xcassets */; };
		8BEEA12FFBF996445E1CB1DC19BF86BB /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		8D50723CB05FE952BF458618F640469C /* RequestParameters.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1CC9DB042BB7452C8DF4780D181AF9EF /* RequestParameters.swift */; };
		8E427B7BB06FB2CE07495196E689AC5B /* Obscurable.swift in Sources */ = {isa = PBXBuildFile; fileRef = F104BE6C6A9888A94273C854EEDB9FE9 /* Obscurable.swift */; };
		8E93A7936C744CB6EEEE8DA3390877E2 /* LifeCycleReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0916B7508E92C6994419DEA44DBD4EF /* LifeCycleReducer.swift */; };
		8EC2CA04459E3A5F98EE63D324720BF8 /* DrawerParticipantView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EF2FD88407950F3C079382A97AE767E /* DrawerParticipantView.swift */; };
		8F29F5649375CCB5181DB0CF1426D6D6 /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C462A53B5181C8E6CC6636F25F9E2D2 /* HTTPMethod.swift */; };
		8F4281C05CD83612D710B051CA108004 /* CallScreenInfoHeaderAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17A0DD6A30532341B683365BFD727109 /* CallScreenInfoHeaderAction.swift */; };
		903596F6C657E89C102EB6F51472DE1F /* PipelineStage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 941AFE87345A1A2B2FE73F2F5141B784 /* PipelineStage.swift */; };
		903E4F4DE6F59563410AD4A5A54C484C /* CalllCompositeRttData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 205EA6DC83E93406D4D1CFEE1E3654FF /* CalllCompositeRttData.swift */; };
		90AC57315952D84BE6AB5B3FF2952789 /* AzureCommunicationUICalling-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FF705F9C575520FF9FD76F5C881C6DC /* AzureCommunicationUICalling-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9199D5F26576ED63DE1858063F8B6F14 /* PipManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2B29DCE4D2A947D29A24C56E0D75F90 /* PipManager.swift */; };
		923CACEB45F780CD4FB3937071D1D6D8 /* NavigationState.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB1A4903AA79B06795C4542AB7485FB1 /* NavigationState.swift */; };
		9270F9C2A6BB15AE540706D6C9ABD7F9 /* ContainerUIHostingController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21622B3E2C0FB25D714EFA3E4920CE83 /* ContainerUIHostingController.swift */; };
		92AD5C91B0836AFF0821AD0147DD8FE6 /* SwiftUI+ViewModifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 45BCD08F067AB985DD78CBCD4E749A2B /* SwiftUI+ViewModifiers.swift */; };
		945B5AD0A88EC7C69C2DA282482192DB /* LinearGradientInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = E3B3E192E42DA21880DC62EA215DB7B3 /* LinearGradientInfo.swift */; };
		94A2A9D819FB7D0FADE118DF2000E390 /* BaseLocalizationProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3A7FB7B5BB557CFC57AC42DC63057DB /* BaseLocalizationProvider.swift */; };
		95DA64658B1E4B7379634DF920403CA7 /* CommunicationTokenCredential.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3A1ABC86FF6BE6569A82921588B3E402 /* CommunicationTokenCredential.swift */; };
		960793915B922E6F9352AA6E96091B3A /* ActivityIndicatorTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74EAFBA1A72000C286263A66C76F2AC6 /* ActivityIndicatorTokenSet.swift */; };
		9633EF16024F08F7177EBD4A516B84B5 /* RequestIdPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4423E83C671B759C5512539D57237C05 /* RequestIdPolicy.swift */; };
		96C3C74EC68BBADC6A12AE116C0D7716 /* ArrayExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1CAFE445C653E0AF4BD938517442C2F3 /* ArrayExtension.swift */; };
		9712F1478FCEF0B1829324D5C275FB7B /* Colors.swift in Sources */ = {isa = PBXBuildFile; fileRef = C54F33F7270ED5AE6F7A5B1BD8AE7878 /* Colors.swift */; };
		97ED16B71E09C0D849654C066D59E772 /* UpdatableOptionsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = D17750201604D3C0371C403D9623B9B3 /* UpdatableOptionsManager.swift */; };
		980BDCEB1715A31D6DC52226EFDEE2B4 /* KeychainUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B90B78A9861159EC7B6BD9BD0B62B75 /* KeychainUtil.swift */; };
		980F484758900A8E26DFCDF08F9B293B /* ValueChangedModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06FC2068C2D9B7937C846DE558DD988C /* ValueChangedModifier.swift */; };
		997ED482F100D24BB3B670DF84463A47 /* AccessibilityIdentifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 262D4FC1818F409DCBD3551A6542A6EE /* AccessibilityIdentifier.swift */; };
		9A198E38343834AC3949C5BE2A6D3399 /* PermissionReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C36F6C6E5D1925404A3822BD531E6AA /* PermissionReducer.swift */; };
		9A580B25FA81C4C3E166C3F37E9D4E88 /* AutoRefreshTokenCredential.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5FC4615733668365907B297184444CD4 /* AutoRefreshTokenCredential.swift */; };
		9A72AFF0F7E6840386BA4C0B4588DD63 /* LobbyErrorHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F7791E220BF4028BC05FE70D69E9090 /* LobbyErrorHeaderViewModel.swift */; };
		9B81CBDD01789BAABFE0939CF4BB6BF7 /* IncomingCallError.swift in Sources */ = {isa = PBXBuildFile; fileRef = B057843310E2BC4D1A11681D2B3D0C89 /* IncomingCallError.swift */; };
		9CB2D264857B9DC90A3668A45B0541CD /* AudioDevicesListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E0EB44C551D89663740A9BFB89BACDFA /* AudioDevicesListViewModel.swift */; };
		9CBA79540EC03C5E6A3E0C0EAE137D63 /* InfoHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2C73009B2782FA4E19C89ECE8DE42BA /* InfoHeaderViewModel.swift */; };
		9D16E7894844FA8CB8EE80A2B485D975 /* OnHoldOverlayViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 673643236C52F7E2127858CE0D342842 /* OnHoldOverlayViewModel.swift */; };
		9E1B57A3E22566C31B66B2996D25E0A3 /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4C17EAC97A56C33D1DCBA96F9022867 /* RetryPolicy.swift */; };
		9EAED3DCCBE05B3F6C261D7A519EB440 /* SupportFormViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C60F13B32E165751CA8F4718EA71D2A /* SupportFormViewModel.swift */; };
		9EBBC551A7B58DA46E2C916D28BC0F25 /* PrimaryButtonViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55119FCF266D0C9EBFBB240A6D46C87C /* PrimaryButtonViewModel.swift */; };
		9EC68CA90BA2C992B9140A505404EE82 /* BannerInfoType.swift in Sources */ = {isa = PBXBuildFile; fileRef = B3C7C3776A3DFC6AD9FCC27D5396DF44 /* BannerInfoType.swift */; };
		9F9386C3632E4B70432B601E7FEC4349 /* LobbyWaitingHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B2BDF2CB10073A7997A16204BDD133D /* LobbyWaitingHeaderView.swift */; };
		A029776B1B125654585699426ED2B04E /* PipelineRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A962D257B2DCC49DBA3298654A61BD3 /* PipelineRequest.swift */; };
		A03D90EA417F6174729E4F8196E36A77 /* AliasTokens.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD76B04491BD820A0DA1E2B43CEDC6E2 /* AliasTokens.swift */; };
		A07FA882113AEC86D2FF72943A935413 /* CallScreenOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F3940DD8AE1F12F6138213A990CCD7C /* CallScreenOptions.swift */; };
		A0C312E4629F955F4F0ECF96846DE6D8 /* PopupMenuSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0A262F8DBAB0D0ADA5D35447063BA98 /* PopupMenuSection.swift */; };
		A0CB06E99DF2728EBC8B4135AB67F151 /* SharingActivityContainerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0712082E8DD32619C6C19BF72BB19C8 /* SharingActivityContainerController.swift */; };
		A0E8362CF9CDB6E59F2B70C49DBA1C1D /* VideoViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7585D83C3D2E9F1E0B576CB6095C0EE7 /* VideoViewManager.swift */; };
		A0EBDB80AD38735DF62DDA2CAD2D7BCA /* ParticipantMenuView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4AC2A6809879809D48466875C2616841 /* ParticipantMenuView.swift */; };
		A248ABEE4E6B53EECB5F3BA42DBD28A7 /* Pods-iOSProject-iOSProjectUITests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 83E02E02FB925819D7301762052BCC4A /* Pods-iOSProject-iOSProjectUITests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A363187B7B1A09BDEC09B045A6417BA3 /* URLSessionTransport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 29D59772F3D34B20F3D5E13E0817B254 /* URLSessionTransport.swift */; };
		A3F18C3D5A9A771AD0CEFE9768FD2572 /* HeadersValidationPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE202518DC955F43703770B45A5927A /* HeadersValidationPolicy.swift */; };
		A4C441CB9DBFE64F801B244BA0A754A4 /* AudioDeviceListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFB59273F188B58A956814DA4FDBE61B /* AudioDeviceListView.swift */; };
		A549BC2BF6AB6A977AFDC6C4D7A18A95 /* BannerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F452CF9330FB3E3E5A3427CD68B893E /* BannerViewModel.swift */; };
		A58FBF8711DBC9C23E548DE08904FE9A /* RttAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDDFF5CD8018A225E1C89D80901744D5 /* RttAction.swift */; };
		A618C1435B5E5370C4E40DE5552B6619 /* CryptoUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0CDA2191AF0E7C82F780C282C816224D /* CryptoUtil.swift */; };
		A6AE0F6CA34966FFF1AFBBC8EAD6824E /* KeybaordResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBA0AFA18700F0D16059DFC2D214E346 /* KeybaordResponder.swift */; };
		A6D8E9DCD84B60C464B4DF35BBD11088 /* DrawerShadowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F2559CE8EBDC6304277729C5FD5B62B5 /* DrawerShadowView.swift */; };
		A7331272FF99E14988990B264EC4D1EE /* OrientationProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9882C331297D666DF83612C2A9BC4337 /* OrientationProvider.swift */; };
		A7CCA2881E3E022D7DCBE01BE97D57AC /* CallingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9EDA7F6A7BE98B46DD1FCF9FB1C5BDD /* CallingService.swift */; };
		A8A5EFBD3F421934466BAEE0E3321AF7 /* CommunicationAccessToken.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4284C28FAE7380E750A92A818ADD0B2C /* CommunicationAccessToken.swift */; };
		A9438F3D65F0DF9B3E8C2D61DFA1B0F9 /* TableViewHeaderFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF4EE540C6CDF62F03E8B54F4860C8B7 /* TableViewHeaderFooterView.swift */; };
		A9D1085AD1E272583E696752EB3BC6BA /* AppPhaseKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0301A4464AF0D7E255FAD06F2C5B8AE3 /* AppPhaseKey.swift */; };
		AA224EA2FA97D6C33EF324CC03DA7CEA /* LeaveCallConfirmationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B951D8B6D4294F7E0A70D7BFCF0CEDA /* LeaveCallConfirmationViewModel.swift */; };
		AA5514F8128359D2B063AC71D0EEFA31 /* URLUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 280D7784580701D3A46D65D9E36C500B /* URLUtil.swift */; };
		ABB7007613FC1621A177A89A05BEE5CE /* CallCompositeCallState.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACD3A3EBB1C1EB3480DE9035D31868CD /* CallCompositeCallState.swift */; };
		ABEBBAE1D9FCB525B59A9F9B44095FD2 /* ToastNotificationState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99C09043F87A7021A9829A31FE06303D /* ToastNotificationState.swift */; };
		AD9EAD878DBA4B32B1E7A8A20B647374 /* AuthenticationPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5164CD72A2B9266F7DA4F055678C4FA /* AuthenticationPolicy.swift */; };
		ADB705E0492E7B7E1746BB257EBCA958 /* LocaleInfoProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = F01A889FBAD31D7921A763022233468E /* LocaleInfoProvider.swift */; };
		ADCCF166BB69C5E6A8B2D1FF464DD650 /* CapabilitiesChangedNotificationMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20D544825A928489013B8802AB174DD5 /* CapabilitiesChangedNotificationMode.swift */; };
		ADD2BAE872977999CD684C4CEE17E2A5 /* CaptionsRttListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7C613D393C7137E783637A2A89D73B87 /* CaptionsRttListViewModel.swift */; };
		AE1340C07972DE692D716057CACB70C6 /* CallKitOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF62620421CF1A8CC28D43CE04A10EFB /* CallKitOptions.swift */; };
		AECC65BC69F0A4BE2C295A7CA569570C /* Reducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A8D08B31B1F8B137E16E26B0112CC47A /* Reducer.swift */; };
		AEE6C966645BB978343FB78C56FECBA6 /* BottomToastViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7BC176FA5AFAF40683ABDABF415FB721 /* BottomToastViewModel.swift */; };
		AF27E8976D275799E4B8D80D20D5E2A2 /* DrawerTitleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F664E0FD672EC99E6B911E2754BDBCD6 /* DrawerTitleView.swift */; };
		AF33BF20BB9142E45C5F26A06A7D83CE /* ParticipantsListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA8D716813D3130FA8106B1CBC836973 /* ParticipantsListViewModel.swift */; };
		B00044F93F8D235CE7359A3287DB5AB5 /* UIImage+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4E8E6731BD3F7A9E606599ACFDAA1C5E /* UIImage+Extensions.swift */; };
		B04C0979DCAA442690A53FC9D6CD24D3 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		B18BADC9C74A4B67C3917F037E111F7C /* CaptionsState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0274128AB684CCAC7D06822C0FBA06F0 /* CaptionsState.swift */; };
		B1AB5500848393708B03BD35765372DF /* Pods-iOSProjectTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C253A2EB0B95B3FD1062CED3E36E95D /* Pods-iOSProjectTests-dummy.m */; };
		B1B7537983144DA2E9CB8E2335F7A433 /* CapabilitiesChangedReason.swift in Sources */ = {isa = PBXBuildFile; fileRef = 306AD77AF822E2EA0EDEE71676243865 /* CapabilitiesChangedReason.swift */; };
		B274ECB8A026F7CB80F033878A4B741F /* AccessibilityProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 531EA0E8D01F61729B3C499E45B3840E /* AccessibilityProvider.swift */; };
		B2B62275E2924073ABDFB6EDEB17835F /* SupportedLocale.swift in Sources */ = {isa = PBXBuildFile; fileRef = C55567F7C85C976E5A389C0BFF8D6830 /* SupportedLocale.swift */; };
		B31140F4CD63C0184B540D0FDD0FE732 /* ButtonDynamicColors.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA072894EA7E403A1E8157D86EE6C9CE /* ButtonDynamicColors.swift */; };
		B32FEC40A9FC4ABF969BD9ED88AF2B89 /* PreviewAreaView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 82B588EE471DD4863C4F3E1827B75F16 /* PreviewAreaView.swift */; };
		B3FFBA782629235B4E967816D48139EE /* XMLTree.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87EC8CD373B0D2683C8B40EFAFDBE3B1 /* XMLTree.swift */; };
		B57ABF7A78579F12BDA1376DE1C34E94 /* PipelineClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65BF1421F6F1F18A74621DBF779AAA44 /* PipelineClient.swift */; };
		B582A75D2DE93B48002277B179829343 /* LoadingOverlayViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 43F1F53555D656DD229B1B798CABEA87 /* LoadingOverlayViewModel.swift */; };
		B5F90F91CFCEAA62A832B8AA77BC33F3 /* CallDiagnosticsState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226FBEA519A8A38135A424309EDF2657 /* CallDiagnosticsState.swift */; };
		B5FE4E9400E78F3A8D622ADBB7535FA8 /* ParticipantInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 144FA1D5AC01C594738AE993AFE3FB17 /* ParticipantInfoModel.swift */; };
		B63509A7BBE1DB4E5A2D83985A8D1045 /* CompositeAvatar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 596F9D63F60626040AEC01E09924B2CA /* CompositeAvatar.swift */; };
		B6FA26E4C6BD8B9EDC4C27AD08A4414E /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9450C7EF18073872DF8B0BF20E01AF74 /* NetworkManager.swift */; };
		B72505F0447E22A038EF53907DD763D6 /* AzureCore-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = FB3BA383AFAC1F032391FEBC4CFC0EE5 /* AzureCore-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B74AFCF43E12BB1900F76A6F63778896 /* SetupScreenOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 26AD500ECC3D9CD8C0C6F55114F085A8 /* SetupScreenOptions.swift */; };
		B76B645BF59DBA8F8744DFE59CE93E56 /* ControlTokenSet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A1C9C94BEC5608D7DE305E4180822A0 /* ControlTokenSet.swift */; };
		B7F0C4A4B585F5490C3B767AC12A64EA /* MessageBarDiagnosticViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1529838C2753DB8D0A725CB14A34C0D /* MessageBarDiagnosticViewModel.swift */; };
		B8E46902C7A9A36380C8AE7E733DBE78 /* OrientationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3ACAA6E7964BD6B4971927811F1BA4B /* OrientationManager.swift */; };
		B9570BDE98AC0190E82423B18F399763 /* DiagnosticsAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3067ACAD5DF9C145E107981C11CC3883 /* DiagnosticsAction.swift */; };
		BA9394F4A669EF0D73AD97711E88099C /* DrawerSelectableItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 617DA3CBB02D35BAC9844F8D6FDE5AF0 /* DrawerSelectableItemView.swift */; };
		BBD03BB34CF95B105414B96A3D613EC7 /* ExpandableDrawer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B42ED0F4919871EE735A996465EFB2 /* ExpandableDrawer.swift */; };
		BC1C657450BBEC012B823B2BF92A2BA6 /* LandscapeAwareKeyboardWatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 47DF492CCAC5D5667C4D1ADBAB7C6D38 /* LandscapeAwareKeyboardWatcher.swift */; };
		BCFE29686FCBABE5953CDDED29F9815A /* CaptionsRttInfoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFED14DAFEAC58EF0FF13D249279019C /* CaptionsRttInfoViewModel.swift */; };
		BD34EC871BC5A38AC0329D3C5AF9AB37 /* AzureCommunicationCommon-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = F026B274B4D4612459F68A558F8B9232 /* AzureCommunicationCommon-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BE7786B367859900E8498453FD8FE3CA /* CapabilityResolutionReason.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4D2597860D550CA0FA0D7917A92EF5D /* CapabilityResolutionReason.swift */; };
		BE967C74F26E171BB209CF1006750A34 /* LobbyOverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB511B1F8C4FA9EA40D159DC863E075E /* LobbyOverlayView.swift */; };
		BF1347C67C3F7458623EC0BA9357C70B /* SwipeActions-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = F281EDBAD41DA89812A4F0E3F6B08F28 /* SwipeActions-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF1B7DD52AEA2D205117621B04AE3D83 /* CancellationToken.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFA59160085822A230D4D3F2CAC27407 /* CancellationToken.swift */; };
		BF1BDF2EC27FCEB110407E89742B2690 /* NavigationReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB8C8B95709332F1A537618BAD51FB77 /* NavigationReducer.swift */; };
		C053D19B3B4FCBCEF284D1A46088CB00 /* Action.swift in Sources */ = {isa = PBXBuildFile; fileRef = BCEE3114197F3B1431067D326CC8A4C3 /* Action.swift */; };
		C115BCAB1CBD85462DD70B6ACFBAF26E /* CaptionsRttInfoCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 194EE3B4F2200717B8907E9424B79BFC /* CaptionsRttInfoCellView.swift */; };
		C24C6924671F1C4AC38E854A9C381F76 /* ParticipantGridViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44C69948F42ADCBF5C82B5B7AA3E4012 /* ParticipantGridViewModel.swift */; };
		C2537DD836556E012D2093CCA87CFE94 /* CallingMiddlewareHandlerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D2A393468C1F2FFCE47A211112EDE4E /* CallingMiddlewareHandlerExtension.swift */; };
		C27C8E871EF77FC740C447CE07303AD1 /* Identifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B0C855E6E711B3B54F4B8F1FD2B9718 /* Identifiers.swift */; };
		C2998C04C6FC0A081EDECC7ECB579A7A /* CompositeViewModelFactoryProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A7ABE8351935FEC3F42FA5CC821B91F /* CompositeViewModelFactoryProtocols.swift */; };
		C2C1DC83D9912679081E8437EF0DEF26 /* Button.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1514DA8A0B7A198B45367C7F44BF6D6 /* Button.swift */; };
		C3F613AE8D9E19767E3AF0EDA78DD6F2 /* RemoteParticipantExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5CB2963F6976CB6B1583C80139DADA21 /* RemoteParticipantExtension.swift */; };
		C5CD03C467A80A7A57CCC6ED94B6E49A /* CallingMiddlewareHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = E494AFE4F4CD3C7CA87384C7235B0ED9 /* CallingMiddlewareHandler.swift */; };
		C5D72626F9080298EB6992E110DC127C /* Pipeline.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0738CDC794B122B909F2CB77D9FF30E4 /* Pipeline.swift */; };
		C60D53B80B2990363A5263716969D2C7 /* UIScrollView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A0C8E9132C915461271F846AEE7C0C4 /* UIScrollView+Extensions.swift */; };
		C6A350BEBCC1DDDBC2F8E0BCD1748270 /* ActivityIndicatorModifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = EE826D52AF3C62D88E8915C5414A6D0C /* ActivityIndicatorModifiers.swift */; };
		C7C1737A9BB41F8B8778D84448091EF9 /* LocalUserState.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC957AE6D4DB18DECF63614A413BA7A6 /* LocalUserState.swift */; };
		C845937161B08CFBC1654BA55D39B920 /* MSFAvatarPresence.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9DC04648E59A69FC8269BFA175E45DB9 /* MSFAvatarPresence.swift */; };
		C8C3C174017433A71570A347BC1C5979 /* LocalizationKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = EADD14CE206148D922688699F4B0C66E /* LocalizationKey.swift */; };
		C8D622C8DE9FE69D2E964874271E346E /* ButtonViewDataState.swift in Sources */ = {isa = PBXBuildFile; fileRef = F80174F9963D4E5D8DFCB82032F35648 /* ButtonViewDataState.swift */; };
		C94F2AF5AC1953AC21D71AD91D770225 /* BannerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C9AB15AEFE48DF18268688F4459844F /* BannerView.swift */; };
		C9A41DF7E78E69E7B9D255E3AF99CA35 /* AvatarGroup.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9888C41B7DD150EC19D9EEFF14FC5631 /* AvatarGroup.swift */; };
		CA901B803EEC54F844BEEAC297F9D587 /* CallHistoryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2928A6C141A717EDD662FED561A0AFCB /* CallHistoryService.swift */; };
		CB6A81C2F4DAB2B9714244700CCDA3C0 /* CallCompositeAudioVideoMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 708438BC86FAA596A5EA9E36EF56148A /* CallCompositeAudioVideoMode.swift */; };
		CBD3525247E7204DC2376341381FCE4D /* LoggingPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A4F477806D5E5E2B30F37AD4BE1A49 /* LoggingPolicy.swift */; };
		CBF1480A9ADDB6235BA03B1241E3D05B /* LockPhoneOrientation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC00BF7CD36D080BCC03D5345BDC8E1F /* LockPhoneOrientation.swift */; };
		CC1F4FAF5CD4E8017C6E4F0CFC19F864 /* CallingSDKInitializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAAD952B552D0690BFCA8C7E658B96D /* CallingSDKInitializer.swift */; };
		CD84297515BFAB6E6CBCD76C684DCB85 /* StoreExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 852CB6DF7B4D54E2BB7CC8B4BBB94717 /* StoreExtensions.swift */; };
		CDE09F3D7FEBC6389A262AEF901FC9F4 /* CallingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 967F0E039F8EB390364AD9B5F3A2749B /* CallingViewModel.swift */; };
		CEBBA78362F881199FFAA41410D9C489 /* UIKit+SwiftUI_interoperability.swift in Sources */ = {isa = PBXBuildFile; fileRef = F014B56A21A122D051BA050708238F7A /* UIKit+SwiftUI_interoperability.swift */; };
		CEF00066B7168CE6385B253FAAEAC24E /* ParticipantGridLayoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 91E7ABAEF8120BD170CA30ACFC5D8F1B /* ParticipantGridLayoutView.swift */; };
		CF1166ACFFA80417403AC070919530D3 /* HeadersPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0CF641A4F8C6F3D58B18E9851F765A8 /* HeadersPolicy.swift */; };
		D04BCBA2E2B5B14554BF25306CC46FBE /* AudioSessionAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56CBA3E5071759DB2C12C47E68A7D883 /* AudioSessionAction.swift */; };
		D26B46820607F8583CB04B7A24C2E5A5 /* CancelBag.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DC7F22CF13B575C14DC2843CAD51A7A /* CancelBag.swift */; };
		D2A9DF57CCB4018BCBE5BB12418EA33B /* CaptionOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32D9E6D248A44E29466AEBE3E6C16868 /* CaptionOptions.swift */; };
		D2E0F69D94FA97C46C622FDA13235665 /* ViewModifer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FFC41A5A1944F5755936835A4FDEC1C /* ViewModifer.swift */; };
		D35BA384F0FE78932D2E93DD1C62BE94 /* AvatarGroupModifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = DCFAF388EA4F1ECC05D192D188DFEE93 /* AvatarGroupModifiers.swift */; };
		D3900ED96AAFAC207D6853EEB4AF3A11 /* SetupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 30F59FD319397F7F3BF782E2A0E6FB84 /* SetupView.swift */; };
		D44F81D792433346FE5965360979E39F /* FluentUIFramework.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E78741BE32DB058AE93ADCCBA6948CA /* FluentUIFramework.swift */; };
		D59689481D5E877B4A031805827509AE /* CallingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95042D657EC783F2B920241557CC2247 /* CallingView.swift */; };
		D62A2BC22B9615439F3D50E1B7987454 /* CaptionsAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74B299A2D09F2578C65AB73C731B5763 /* CaptionsAction.swift */; };
		D7CBC453D215E76201BEC71E500B67AF /* LocalizationOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6868E914AD372E6D5CC11202578F453 /* LocalizationOptions.swift */; };
		D90077B85883DB66BDBA0019AC1A10E6 /* ButtonViewData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37C9433A53B7FC888E96775D61B094DC /* ButtonViewData.swift */; };
		DA290F587FD6B2242B1BB7365EFB0778 /* HTTPHeader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 878629BA144808F20AA70C561DA96B78 /* HTTPHeader.swift */; };
		DACF550D77C9CA4F9C1E5EE16C031CBE /* SwipeActionModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 389CABF0F421788C4E7E1DCD4F5ABA50 /* SwipeActionModifier.swift */; };
		DB5658E018A48FFD562EDEF715A208C1 /* CaptionsRttDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 60EB9D9E66A3F6F6812B402F23CF6D72 /* CaptionsRttDataManager.swift */; };
		DB7457EF185A0AC8C3CB05A671781E81 /* ThrottleMiddleware.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B6FFBE2E8EBA1794FB724F48964DB51 /* ThrottleMiddleware.swift */; };
		DBBE52890B1247FB2BA899A21A61E877 /* CaptionsAndRttLandscapeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13C5844C405EF38D94250F837994ED92 /* CaptionsAndRttLandscapeView.swift */; };
		DBD530FDB4BC9771689682CB06C367C7 /* LeaveCallConfirmationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35A615C7ADDD5851E814B5CF71FA1C99 /* LeaveCallConfirmationView.swift */; };
		DC3523C06FFE0A7D9C4C20331F57873F /* LoadingOverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 82EF81BA07C5A8CCD7C478E6326FCC03 /* LoadingOverlayView.swift */; };
		DC3C9797AE6878254A17EEAE1275418D /* UIWindowExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACE425916CFC4C433A43D6426AA53C57 /* UIWindowExtension.swift */; };
		DCC9075E1558AB5E39F8F9E179CAFC0A /* RemoteParticipantsEventsAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 383D8B37BAA305E3CAC8AAECE10FC9EA /* RemoteParticipantsEventsAdapter.swift */; };
		DCE2536547642376FDF6E13C8BB8F04F /* DrawerPresentationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC3E45754487C92C098E3F8482907685 /* DrawerPresentationController.swift */; };
		DDCADEDF9579952FD5F1624C10B92F5A /* ButtonViewDataReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2FB7226D26E5565722E65CCB7A911307 /* ButtonViewDataReducer.swift */; };
		DED3F53D2AF0CD0F864ED180DAF58184 /* CallScreenInfoHeaderReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A92C037CBCD0B65A68610A1397E6ECB /* CallScreenInfoHeaderReducer.swift */; };
		DF56F1F23E31237E43B1FBA392BD4A14 /* PopupMenuProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5ADCEA4BD476BAC9E7D27760C472A3F5 /* PopupMenuProtocols.swift */; };
		DF59291E0D8390426B3ECE5AE65BBC1E /* LobbyWaitingHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE62285A8986240D418B928205FB0F8B /* LobbyWaitingHeaderViewModel.swift */; };
		DF5F955B17E47ACFA9D51207755FAE0F /* DrawerTransitionAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4B660B5DAB93F47D1B002FF8327DFEF /* DrawerTransitionAnimator.swift */; };
		DFE4576440237C3260F24EAAF8B63B37 /* AzureDate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 636F88A1B58EFDD74E5FBDD6AA7867F2 /* AzureDate.swift */; };
		E0237CEFA04C715C99AA292FE83977A0 /* Caller.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1EAC9F4A1A49EF099462903C61D3A483 /* Caller.swift */; };
		E18C6A001DFC6927A44D4D8882E8C1FE /* SupportFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA553BDCA19BCAC7527E3CA1BAD9D519 /* SupportFormView.swift */; };
		E19800BEEE6A38F84E5BC0893EA4A1A9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		E24434CCD310B0B81872F9D17404DE12 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 94F313C6263B4527440AEF93A30811F9 /* Localizable.strings */; };
		E31477CF0AF725ED75257F5F371D219D /* CaptionsErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6531D67EE076410ADD78162639954F4C /* CaptionsErrorView.swift */; };
		E335C0D824EC0F2E5F3FC2C8B2796A9B /* RemoteParticipantsAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06119083CF4415F895393C2B26555F91 /* RemoteParticipantsAction.swift */; };
		E3AB93B567DD80819D5820DC0838BE26 /* ParticipantGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFEA35640AC140925F2160D468256639 /* ParticipantGridView.swift */; };
		E4F17D70EF97A7D68B1B1BD148E2214B /* PipelineContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C4CDD044B0B0B362242477461A1D887 /* PipelineContext.swift */; };
		E53890775A6CF6797D0D505C81EED172 /* DrawerListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB133885A3F907D1DF292B6DBE0ED66E /* DrawerListView.swift */; };
		E5864F334CF5348981D15460F32BB9F2 /* Store.swift in Sources */ = {isa = PBXBuildFile; fileRef = 943A8EDFF888C1E9C1DF659C2A972A46 /* Store.swift */; };
		E5FDA0931ABB44311D95562B899195ED /* DebugInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84BB5B98C93B913663B1A4BF3E5D9016 /* DebugInfo.swift */; };
		E62E384E921B31180E707E4BBAC7CB5C /* ControlBarViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4352CBF9DBC48AF71723F338BAD43CDD /* ControlBarViewModel.swift */; };
		E65B2A3502FB1E1C0F3D099BF6C9281F /* PrimaryButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = EDD93D840930CB9A58E7C2948A203B73 /* PrimaryButton.swift */; };
		E6A0081EF2354448852D843464B3ACCA /* ContentDecodePolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = F48461459D817F1768F10E0326ABDAB7 /* ContentDecodePolicy.swift */; };
		E769F5CF5BD999C4977C510BC9A01033 /* OverlayViewModelProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BBE34CC70AFD621A825E6A45E886DE3 /* OverlayViewModelProtocol.swift */; };
		E7BBB593F263D3153C5E3448A4B5977C /* PreviewAreaViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0137455B49A97371106A1571801083C /* PreviewAreaViewModel.swift */; };
		E9C548B81CAAF6D5AA5A0876D79094F0 /* PopupModalView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D902CC62F7F2FED9BD8D043C4B9148D /* PopupModalView.swift */; };
		EAA7AD200A405013C5494F4DF982F739 /* DataStringConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = F77927DBD38E462175E5B4220BC45850 /* DataStringConvertible.swift */; };
		EB3216117924B208DD7ACDCA4619E622 /* RemoteParticipantsState.swift in Sources */ = {isa = PBXBuildFile; fileRef = EA418E2843D9E62F06D607FC0724031C /* RemoteParticipantsState.swift */; };
		EDEBB0F15FC4D7192DB24AC0D9405205 /* TransportOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6E8CE915C73A74556C09B201EADECC9 /* TransportOptions.swift */; };
		F0209313CA71FAF9B05B8AC0F51F800D /* ResizingHandleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F2B2AA8A262B395B05D77D9EF2BCD17B /* ResizingHandleView.swift */; };
		F03CA69C7137D5D2818C7AB17B992A64 /* ParticipantCapabilityType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A68E49E62ACD9C3D5DEEB9572C3E4974 /* ParticipantCapabilityType.swift */; };
		F1475EE661F4E3AB8AE5D284D3C31A2A /* FontInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 78777E17C95C21DFAF5FA16332036A41 /* FontInfo.swift */; };
		F24750BD78018EFE82012D06945CEBAD /* TransportStage.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2317AC05263AFC4A8B512518BBF793B /* TransportStage.swift */; };
		F313E5A0927808456AB990F38FF09FC9 /* CustomTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31E0CF6DAF1E84826C634A18A60954B4 /* CustomTextField.swift */; };
		F32CBE3529B7052DC39EE6A3CE9B8628 /* CaptionsLanguageListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1B3E2B0B90D1A7BFD51C6D889DBCF74 /* CaptionsLanguageListView.swift */; };
		F339AE2403ED039AC7AD049485609F75 /* LeaveCallConfirmationMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F4C75EFEE51C98C333DE6B870DE5AB /* LeaveCallConfirmationMode.swift */; };
		F3A26AF2429D78D470A1F1125388D881 /* BottomToastView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED27BEB9573F9494E493ED01EF3332E6 /* BottomToastView.swift */; };
		F40E0E1D711EF6FF514A6A8B610B95CA /* CallDiagnosticsReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 219FD1924126E1119F10E7B98DEF4763 /* CallDiagnosticsReducer.swift */; };
		F519B3BD976B0356616CDD5E4B7E364B /* DimmingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49CFB64DADC6099E722D28E4E4DCCAB1 /* DimmingView.swift */; };
		F58840BC231A5E599253641B3BBAAB33 /* CompositeViewFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7F42A5449586A5CDF7FFCC2613B8386 /* CompositeViewFactory.swift */; };
		F5B26AD3442784E31DD9FBB873D40391 /* AvatarModifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 29E57BFA29CF2B403BED3DB1BD83FDA1 /* AvatarModifiers.swift */; };
		F67230B12A8461BC61D2BC32FF48C6CD /* ErrorReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5A60CAF27A6D6477DC41D42EF66FEC2 /* ErrorReducer.swift */; };
		F7934210185E2437204B29F85C6AE74D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		F79F0E2AEF260D8F9B31B33837D6C8A9 /* CommunicationTokenCredentialProviding.swift in Sources */ = {isa = PBXBuildFile; fileRef = D032AFA98F2B30BDC8E96F4147BD4B62 /* CommunicationTokenCredentialProviding.swift */; };
		F7CCE57AD55A710FBDB59EE06FDA336E /* EnvironmentValuesExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FF3887B3B601C3932BE9AA47D95F7D2 /* EnvironmentValuesExtension.swift */; };
		F7E5694A11A0C05AD3E18FBFBED44439 /* CompositeButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = EC4A373D3270D2DEF3C8658460600A61 /* CompositeButton.swift */; };
		F828E9F6307C16CF23A1AE33B103863C /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 3460057AB7FC55D43DAD719B427A91F6 /* Localizable.strings */; };
		F83D92144FBA5A1CBD3298E8091DCAC3 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 998E99C627AA67517378F54138BAEC82 /* Logger.swift */; };
		F8C9CA2F6449712F3E62B89999B3872E /* LobbyOverlayViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CA8D88AB94D8AD39F264A7F56D2A884 /* LobbyOverlayViewModel.swift */; };
		F99A7B2B475F0D6A9E127FEDAA502F82 /* CompositeErrorManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 172690718843666EDCCE06A30502DE11 /* CompositeErrorManager.swift */; };
		FA70CBBD583B9638EFB5ECD6B71D6D9C /* AppLifeCycleManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2773551EC47EDAC57B6775E00C2ED394 /* AppLifeCycleManager.swift */; };
		FA7406CB965C98B9F9E4F78590B8C17D /* LocalUserAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C096B3D9CE8B83F8C5468C5FAB40A6D /* LocalUserAction.swift */; };
		FAA700E2B6FC2DEA93E2E94131C36026 /* ParticipantsListCellViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB744CF8EDA2762E0B6DB6E8F39926D7 /* ParticipantsListCellViewModel.swift */; };
		FB212438A41F7D50A45573F9B101F84D /* StringConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 50DCAD23FC62D0A29CFD2593BA42E2DA /* StringConstants.swift */; };
		FB97DF998AE56E03A8B37F80C59F2C22 /* PipelineResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = E9F4C23561463D8B5A6E56840F88BEAD /* PipelineResponse.swift */; };
		FC081D9AEAF8DE8D3D3DEBAC196D3725 /* CallStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05247F25EE72B97582CFE38B8388E0AB /* CallStateManager.swift */; };
		FCB1D72DE6F4AB5727747F7573C1FE7A /* LobbyErrorHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51853927E49C4E82CA25617521D8688F /* LobbyErrorHeaderView.swift */; };
		FCCED618656E5F6F019784872D36E1BD /* BottomSheetController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08BED95B2C9BA29F65F84CBCF901E136 /* BottomSheetController.swift */; };
		FD934C6BDF4CD9161B5FB83F391D3C6E /* CallCompositeOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = B399BBBE8E4E00C64C8E28CF3BB81D68 /* CallCompositeOptions.swift */; };
		FDD377595A5FA4A1C5423B18C647D48B /* CallingSDKWrapperProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3BDB9A196109E2377F7B394312BA8411 /* CallingSDKWrapperProtocol.swift */; };
		FF6FE58370C1804E51F9BACEE95361A8 /* Separator.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD260F08947A8AC8689CC28B309D1A6 /* Separator.swift */; };
		FFC9550149952E8E76182293A4ADF6F3 /* BannerTextViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F35BB53E051EDDF790EAA717C5AD3D7C /* BannerTextViewModel.swift */; };
		FFCC08698A631203ACD36044D3811B47 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0619FF6F97266E7118C5FA5941CC13F1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDF34E949B35844DA11DAD701D3ACA7E;
			remoteInfo = SwipeActions;
		};
		088C2E19F4D91AFA170C36D0763ED716 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17D2EB69FA839BA1C46085F1047783CB;
			remoteInfo = AzureCommunicationCalling;
		};
		1B5096EDE33C89885F49393410B3B251 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 07A6CC1D379046F22ADCE2752CDEF14A;
			remoteInfo = AzureCommunicationCommon;
		};
		1BDAB1CD1EADD01298C90749CAD0E927 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EF3D57598B2F6B611B951D5BC18E691F;
			remoteInfo = AzureCore;
		};
		32C433BFE03771F3FA91F51AB289B6AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 25584D86D2424AB817DB16B37F1A9706;
			remoteInfo = "Pods-iOSProject";
		};
		573E8727D7C2E8AEEE10D75BE6D0EC01 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2349512128E8343A186BC45D4E7BDFAA;
			remoteInfo = MicrosoftFluentUI;
		};
		997DEF575962B371768D4C92D9887B66 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88C445CF76EB60DD5A2AFA17042F471A;
			remoteInfo = AzureCommunicationUICalling;
		};
		9C7EBC2055043319D28376144E186886 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17D2EB69FA839BA1C46085F1047783CB;
			remoteInfo = AzureCommunicationCalling;
		};
		B15589004DFD598CFF545A003378DC1C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EF3D57598B2F6B611B951D5BC18E691F;
			remoteInfo = AzureCore;
		};
		B5875060BE78221DA93587B5CF1DB9C9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88C445CF76EB60DD5A2AFA17042F471A;
			remoteInfo = AzureCommunicationUICalling;
		};
		C55D9C08B93D17D62647C8215F0D664F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 07A6CC1D379046F22ADCE2752CDEF14A;
			remoteInfo = AzureCommunicationCommon;
		};
		C8FC637B326613CEF78634A2D9143985 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B2C551DE8A6F4F26DF0F188E3BD28D81;
			remoteInfo = "MicrosoftFluentUI-FluentUIResources-ios";
		};
		C98525B04DC5F826D967728B8A52689D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 07A6CC1D379046F22ADCE2752CDEF14A;
			remoteInfo = AzureCommunicationCommon;
		};
		CB3C652FAEAE27ECC5E2487B094562E3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EF3D57598B2F6B611B951D5BC18E691F;
			remoteInfo = AzureCore;
		};
		DC52956F2C78938B1D4E3AB46BB52EAA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2349512128E8343A186BC45D4E7BDFAA;
			remoteInfo = MicrosoftFluentUI;
		};
		E0FA750CC6ACF9117B834C7F20C7E079 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17D2EB69FA839BA1C46085F1047783CB;
			remoteInfo = AzureCommunicationCalling;
		};
		E76DF037B2BD3BD21442F3249536681E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2349512128E8343A186BC45D4E7BDFAA;
			remoteInfo = MicrosoftFluentUI;
		};
		F1DF4CFC83009D6C6394AE006136A52E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDF34E949B35844DA11DAD701D3ACA7E;
			remoteInfo = SwipeActions;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0080BC482A77463BAA32B97A9315BDDC /* CallInfoModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallInfoModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CallInfoModel.swift; sourceTree = "<group>"; };
		011E504D718112F73B239A9B72981DAD /* ReachabilityManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ReachabilityManager.swift; path = sdk/core/AzureCore/Source/Util/ReachabilityManager.swift; sourceTree = "<group>"; };
		01E187A85DCE070B8998DAABB8D66120 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = tr.lproj/Localizable.strings; sourceTree = "<group>"; };
		0274128AB684CCAC7D06822C0FBA06F0 /* CaptionsState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/CaptionsState.swift; sourceTree = "<group>"; };
		027F46675C7F287E56408C08C15C73D5 /* DrawerViewModels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerViewModels.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/DrawerViewModels.swift; sourceTree = "<group>"; };
		0301A4464AF0D7E255FAD06F2C5B8AE3 /* AppPhaseKey.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppPhaseKey.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Utilities/AppPhaseKey.swift; sourceTree = "<group>"; };
		0393B1EE7DB441D6C35A80725513A901 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "nl-NL.lproj/Localizable.strings"; sourceTree = "<group>"; };
		041A9546BC831DD4903B886FB8B28807 /* Icon.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Icon.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/FluentUI/Icon.swift; sourceTree = "<group>"; };
		042096D1657727F5DC351E684915A1B8 /* ACSVideoStreamRenderer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSVideoStreamRenderer.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h"; sourceTree = "<group>"; };
		046BE0A5D32340465034A0DF75B74940 /* AzureCommunicationCommon */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AzureCommunicationCommon; path = AzureCommunicationCommon.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		04CBF581A99090BE8FA2F56860F8FED9 /* MicrosoftFluentUI.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MicrosoftFluentUI.modulemap; sourceTree = "<group>"; };
		04D9C539A6E4A7FF5C0C554EA23FC732 /* SetParticipantViewDataError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetParticipantViewDataError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/SetParticipantViewDataError.swift; sourceTree = "<group>"; };
		05247F25EE72B97582CFE38B8388E0AB /* CallStateManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallStateManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CallStateManager.swift; sourceTree = "<group>"; };
		06119083CF4415F895393C2B26555F91 /* RemoteParticipantsAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantsAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/RemoteParticipantsAction.swift; sourceTree = "<group>"; };
		063677845578DE45849A2145570283C9 /* IconProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Style/IconProvider.swift; sourceTree = "<group>"; };
		063FADACC276E44058ACBAA52A01152E /* CallConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallConfiguration.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallConfiguration.swift; sourceTree = "<group>"; };
		068739BFD2E8849C70391A0DE1E27E17 /* Middleware.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Middleware.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/Middleware.swift; sourceTree = "<group>"; };
		0696668008360E15891ACE87989427E0 /* ACSStreamSize.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSStreamSize.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h"; sourceTree = "<group>"; };
		06FC2068C2D9B7937C846DE558DD988C /* ValueChangedModifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueChangedModifier.swift; path = Sources/SwipeActions/ViewModifiers/ValueChangedModifier.swift; sourceTree = "<group>"; };
		0738CDC794B122B909F2CB77D9FF30E4 /* Pipeline.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Pipeline.swift; path = sdk/core/AzureCore/Source/Pipeline/Pipeline.swift; sourceTree = "<group>"; };
		07B9394E28B761F5D147D58A4B28D149 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = id.lproj/Localizable.strings; sourceTree = "<group>"; };
		0841F2E163FBD3258C7CFEA3CF3FBBE3 /* CustomButtonViewData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CustomButtonViewData.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CustomButtonViewData.swift; sourceTree = "<group>"; };
		08976A16DD59F1122A20227BEC83FCBF /* AzureCore-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCore-prefix.pch"; sourceTree = "<group>"; };
		08BED95B2C9BA29F65F84CBCF901E136 /* BottomSheetController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BottomSheetController.swift; path = "ios/FluentUI/Bottom Sheet/BottomSheetController.swift"; sourceTree = "<group>"; };
		0A0C8E9132C915461271F846AEE7C0C4 /* UIScrollView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIScrollView+Extensions.swift"; path = "ios/FluentUI/Extensions/UIScrollView+Extensions.swift"; sourceTree = "<group>"; };
		0A7ABE8351935FEC3F42FA5CC821B91F /* CompositeViewModelFactoryProtocols.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeViewModelFactoryProtocols.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Factories/CompositeViewModelFactoryProtocols.swift; sourceTree = "<group>"; };
		0AAB6A9BF52DF5BCB57CDDA430A8192A /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		0B12CC5A0B15BCA3FED5ED7A7125AD77 /* DiagnosticConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DiagnosticConfig.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/DiagnosticConfig.swift; sourceTree = "<group>"; };
		0C8A62FD073875D50F76D9A5A657A69C /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "es-MX.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		0CCDB10E011B8B69D78A59A413667628 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = hi.lproj/Localizable.strings; sourceTree = "<group>"; };
		0CDA2191AF0E7C82F780C282C816224D /* CryptoUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CryptoUtil.swift; path = sdk/core/AzureCore/Source/Util/CryptoUtil.swift; sourceTree = "<group>"; };
		0D0B3E335A380AA87D4AC3FB862125C6 /* AppStateReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppStateReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/AppStateReducer.swift; sourceTree = "<group>"; };
		0D26A324599FE6B579AA01C7DC68A29B /* Pods-iOSProjectTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProjectTests-Info.plist"; sourceTree = "<group>"; };
		1011ECE09779CE830E443699EAB17ECF /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = uk.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		102608995FA3ECFBF65D8E15E4ABFD07 /* Assets.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = Assets.xcassets; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Assets.xcassets; sourceTree = "<group>"; };
		10AC7C2113662A725FAA3E1389F6BA70 /* TokenizedControl.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TokenizedControl.swift; path = ios/FluentUI/Core/Theme/Tokens/TokenizedControl.swift; sourceTree = "<group>"; };
		11B8A2F72EA9743D1744DA44D2D9B93B /* AvatarViewManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarViewManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/AvatarViewManager.swift; sourceTree = "<group>"; };
		11C0259A00F0E211FFD4EE6930AF97C2 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "ja-JP.lproj/Localizable.strings"; sourceTree = "<group>"; };
		1203542E28AB32D844C7AA6879500F4E /* CallDurationManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallDurationManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CallDurationManager.swift; sourceTree = "<group>"; };
		12C368BF65434904B90FCAF82BA91ABC /* PermissionAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PermissionAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/PermissionAction.swift; sourceTree = "<group>"; };
		13455CE0A261CCF3D58003DD1E63736F /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		13833A3605E4A28CC07DFDD2445F421C /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ca.lproj/Localizable.strings; sourceTree = "<group>"; };
		13935B8E8FEF162E9E7A0E9846F97225 /* AzureCore.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCore.release.xcconfig; sourceTree = "<group>"; };
		13A1DAA31EA4071EE5EE8A87D8065E33 /* BannerTextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BannerTextView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Banner/BannerTextView.swift; sourceTree = "<group>"; };
		13C5844C405EF38D94250F837994ED92 /* CaptionsAndRttLandscapeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsAndRttLandscapeView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsAndRttLandscapeView.swift; sourceTree = "<group>"; };
		144FA1D5AC01C594738AE993AFE3FB17 /* ParticipantInfoModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantInfoModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/ParticipantInfoModel.swift; sourceTree = "<group>"; };
		1633885AE7332E24C809481268E3D698 /* AzureCommunicationCalling.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; path = AzureCommunicationCalling.xcframework; sourceTree = "<group>"; };
		16F7A9061740284EA7D9EB068AC365CB /* MoreCallOptionsListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MoreCallOptionsListView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/MoreCallOptions/MoreCallOptionsListView.swift; sourceTree = "<group>"; };
		171C078557A25531933FDE37281D5857 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = vi.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		172690718843666EDCCE06A30502DE11 /* CompositeErrorManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeErrorManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CompositeErrorManager.swift; sourceTree = "<group>"; };
		177F54C04B7D49F51BF95447C8F0730E /* CircleCutout.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CircleCutout.swift; path = ios/FluentUI/Avatar/CircleCutout.swift; sourceTree = "<group>"; };
		17A0DD6A30532341B683365BFD727109 /* CallScreenInfoHeaderAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenInfoHeaderAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/CallScreenInfoHeaderAction.swift; sourceTree = "<group>"; };
		17D47BFC91D1F0CDA499B5A0919725E1 /* BottomSheetPassthroughView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BottomSheetPassthroughView.swift; path = "ios/FluentUI/Bottom Sheet/BottomSheetPassthroughView.swift"; sourceTree = "<group>"; };
		18659C0FC416310F1B04064FAFA4A419 /* VisibilityAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VisibilityAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/VisibilityAction.swift; sourceTree = "<group>"; };
		18A4F477806D5E5E2B30F37AD4BE1A49 /* LoggingPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LoggingPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/LoggingPolicy.swift; sourceTree = "<group>"; };
		193F59C267BDC2954DCBD29D8FDDD40F /* DraggableLocalVideoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DraggableLocalVideoView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/DraggableLocalVideoView.swift; sourceTree = "<group>"; };
		194EE3B4F2200717B8907E9424B79BFC /* CaptionsRttInfoCellView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttInfoCellView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsRttInfoCellView.swift; sourceTree = "<group>"; };
		1997DC2FA21B7EFA5ACE0B182B731450 /* RegexUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RegexUtil.swift; path = sdk/core/AzureCore/Source/Util/RegexUtil.swift; sourceTree = "<group>"; };
		1AA6690CD1D0A8BF3997FFA33BD173AD /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "pt-BR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		1B6353F50701D842A002FF9B8E859D1B /* CustomAlert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CustomAlert.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/CustomAlert/CustomAlert.swift; sourceTree = "<group>"; };
		1B90B78A9861159EC7B6BD9BD0B62B75 /* KeychainUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = KeychainUtil.swift; path = sdk/core/AzureCore/Source/Util/KeychainUtil.swift; sourceTree = "<group>"; };
		1C18A8AD2EFDFB837E57DA856DDACFC5 /* ACSFeatures.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSFeatures.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/ACSFeatures.h"; sourceTree = "<group>"; };
		1CAFE445C653E0AF4BD938517442C2F3 /* ArrayExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ArrayExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/ArrayExtension.swift; sourceTree = "<group>"; };
		1CC9DB042BB7452C8DF4780D181AF9EF /* RequestParameters.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RequestParameters.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/RequestParameters.swift; sourceTree = "<group>"; };
		1D902CC62F7F2FED9BD8D043C4B9148D /* PopupModalView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupModalView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/PopupModalView.swift; sourceTree = "<group>"; };
		1E53DC65DE8E9633668A2386576EC8CE /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		1EAC9F4A1A49EF099462903C61D3A483 /* Caller.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Caller.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/Caller.swift; sourceTree = "<group>"; };
		1F9A5C642CB238E19627D5BBC76D9986 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "es-MX.lproj/Localizable.strings"; sourceTree = "<group>"; };
		1FA1E5CC4365B53E5625126CE41A132B /* SwipeActions.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SwipeActions.modulemap; sourceTree = "<group>"; };
		205EA6DC83E93406D4D1CFEE1E3654FF /* CalllCompositeRttData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CalllCompositeRttData.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CalllCompositeRttData.swift; sourceTree = "<group>"; };
		20D544825A928489013B8802AB174DD5 /* CapabilitiesChangedNotificationMode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapabilitiesChangedNotificationMode.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CapabilitiesChangedNotificationMode.swift; sourceTree = "<group>"; };
		2117D4B5A8620CB0095849CCB6E80C47 /* DrawerGenericItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerGenericItemView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/DrawerGenericItemView.swift; sourceTree = "<group>"; };
		21622B3E2C0FB25D714EFA3E4920CE83 /* ContainerUIHostingController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ContainerUIHostingController.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Container/ContainerUIHostingController.swift; sourceTree = "<group>"; };
		219FD1924126E1119F10E7B98DEF4763 /* CallDiagnosticsReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallDiagnosticsReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/CallDiagnosticsReducer.swift; sourceTree = "<group>"; };
		226FBEA519A8A38135A424309EDF2657 /* CallDiagnosticsState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallDiagnosticsState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/CallDiagnosticsState.swift; sourceTree = "<group>"; };
		227378F30354FFDF9EB8590194AC7ED6 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = nb.lproj/Localizable.strings; sourceTree = "<group>"; };
		22F5552064955997B1FF9FDBD6A4B122 /* PopupMenuSectionHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuSectionHeaderView.swift; path = "ios/FluentUI/Popup Menu/PopupMenuSectionHeaderView.swift"; sourceTree = "<group>"; };
		238398EADAF89E6189D854EB9582F9B8 /* AzureCommunicationUICalling.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationUICalling.debug.xcconfig; sourceTree = "<group>"; };
		2399421409BFB50267C72BF62792446D /* Pods-iOSProject-iOSProjectUITests-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-iOSProject-iOSProjectUITests-frameworks.sh"; sourceTree = "<group>"; };
		240C73B422C7877840DAEBD4742F091D /* AzureCommunicationCalling.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationCalling.debug.xcconfig; sourceTree = "<group>"; };
		262D4FC1818F409DCBD3551A6542A6EE /* AccessibilityIdentifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AccessibilityIdentifier.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/AccessibilityIdentifier.swift; sourceTree = "<group>"; };
		269F416E43BE05BE3AEB15562335209C /* ACSParticipantStateExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ACSParticipantStateExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/ACSParticipantStateExtension.swift; sourceTree = "<group>"; };
		26AD500ECC3D9CD8C0C6F55114F085A8 /* SetupScreenOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetupScreenOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/SetupScreenOptions.swift; sourceTree = "<group>"; };
		2773551EC47EDAC57B6775E00C2ED394 /* AppLifeCycleManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppLifeCycleManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/AppLifeCycleManager.swift; sourceTree = "<group>"; };
		278EC9370F396F8BCA5ACD3787243BA1 /* PopupMenuItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuItem.swift; path = "ios/FluentUI/Popup Menu/PopupMenuItem.swift"; sourceTree = "<group>"; };
		279FB395473B3C0BE404C395A17EF437 /* CallHistoryRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallHistoryRecord.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallHistoryRecord.swift; sourceTree = "<group>"; };
		280D7784580701D3A46D65D9E36C500B /* URLUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLUtil.swift; path = sdk/core/AzureCore/Source/Util/URLUtil.swift; sourceTree = "<group>"; };
		28E527D402097C1D2F1D9EAAE0FA6F69 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "pt-BR.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		2902D8EE5D7ADF5746BFADA4C69D948F /* LocalizationProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalizationProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Style/LocalizationProvider.swift; sourceTree = "<group>"; };
		2928A6C141A717EDD662FED561A0AFCB /* CallHistoryService.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallHistoryService.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/CallHistoryService.swift; sourceTree = "<group>"; };
		29B80483762E8C6394430ECED0E38DAD /* CaptionsReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/CaptionsReducer.swift; sourceTree = "<group>"; };
		29D59772F3D34B20F3D5E13E0817B254 /* URLSessionTransport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLSessionTransport.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/URLSessionTransport.swift; sourceTree = "<group>"; };
		29E57BFA29CF2B403BED3DB1BD83FDA1 /* AvatarModifiers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarModifiers.swift; path = ios/FluentUI/Avatar/AvatarModifiers.swift; sourceTree = "<group>"; };
		2A0569FC6542319D29A5F4C7D66F6F5C /* AzureCore */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AzureCore; path = AzureCore.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2A28779FEA75D186EF896EF16A8ECC60 /* FluentUIHostingController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FluentUIHostingController.swift; path = ios/FluentUI/Core/FluentUIHostingController.swift; sourceTree = "<group>"; };
		2A6B92252CF785FBCE4AF821371897AD /* Pods-iOSProject-iOSProjectUITests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-iOSProject-iOSProjectUITests-acknowledgements.markdown"; sourceTree = "<group>"; };
		2AFC20F15296528F104AF39E5CEB1835 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		2B251147CB69E271004FEC65D997D86F /* RttState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RttState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/RttState.swift; sourceTree = "<group>"; };
		2BAAD952B552D0690BFCA8C7E658B96D /* CallingSDKInitializer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingSDKInitializer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/CallingSDKInitializer.swift; sourceTree = "<group>"; };
		2BAE8250C171D3BA639B7D7B97F47011 /* IconWithLabelButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconWithLabelButton.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/IconWithLabelButton.swift; sourceTree = "<group>"; };
		2BF668215FADF3B714F4718F3791FAF6 /* ACSVideoStreamRenderer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSVideoStreamRenderer.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h"; sourceTree = "<group>"; };
		2C462A53B5181C8E6CC6636F25F9E2D2 /* HTTPMethod.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTTPMethod.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/HTTPMethod.swift; sourceTree = "<group>"; };
		2C94F2B36E6A2FDCFE522B3E6B2D4238 /* ParticipantGridCellViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridCellViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/Cell/ParticipantGridCellViewModel.swift; sourceTree = "<group>"; };
		2DCE6587E8613AB0EBAC85C0F2E21406 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		2DE0809D6E8F91E5D2CD6A5099D53334 /* Pods-iOSProjectTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProjectTests.debug.xcconfig"; sourceTree = "<group>"; };
		2EE96CD2D9B1531C227835032BA56783 /* AzureCommunicationCommon-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AzureCommunicationCommon-Info.plist"; sourceTree = "<group>"; };
		2F5448B6BDB3FB53E36B8A4142376E35 /* ACSCallEndReasonExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ACSCallEndReasonExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/ACSCallEndReasonExtension.swift; sourceTree = "<group>"; };
		2F610B67E35AA835DEDE2858EF7D6C9E /* Pods-iOSProject-iOSProjectUITests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProject-iOSProjectUITests-Info.plist"; sourceTree = "<group>"; };
		2F7791E220BF4028BC05FE70D69E9090 /* LobbyErrorHeaderViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyErrorHeaderViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/LobbyErrorHeaderViewModel.swift; sourceTree = "<group>"; };
		2FB7226D26E5565722E65CCB7A911307 /* ButtonViewDataReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonViewDataReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/ButtonViewDataReducer.swift; sourceTree = "<group>"; };
		2FE143DAF7581E7C92674E75F7017A6A /* StringUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StringUtil.swift; path = sdk/core/AzureCore/Source/Util/StringUtil.swift; sourceTree = "<group>"; };
		3019C36E560277B5F3C1E6700ECCA637 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = sv.lproj/Localizable.strings; sourceTree = "<group>"; };
		3067ACAD5DF9C145E107981C11CC3883 /* DiagnosticsAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DiagnosticsAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/DiagnosticsAction.swift; sourceTree = "<group>"; };
		306AD77AF822E2EA0EDEE71676243865 /* CapabilitiesChangedReason.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapabilitiesChangedReason.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CapabilitiesChangedReason.swift; sourceTree = "<group>"; };
		30F59FD319397F7F3BF782E2A0E6FB84 /* SetupView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetupView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupView.swift; sourceTree = "<group>"; };
		31E0CF6DAF1E84826C634A18A60954B4 /* CustomTextField.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CustomTextField.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/CustomTextField.swift; sourceTree = "<group>"; };
		32D9E6D248A44E29466AEBE3E6C16868 /* CaptionOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CaptionOptions.swift; sourceTree = "<group>"; };
		32E5AB2D1D4125269E7EFD3A59E27FF3 /* CommunicationCloudEnvironment.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationCloudEnvironment.swift; path = sdk/communication/AzureCommunicationCommon/Source/CommunicationCloudEnvironment.swift; sourceTree = "<group>"; };
		33DB302B50426EF421D2F53601912405 /* ErrorInfoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ErrorInfoViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Error/ErrorInfoViewModel.swift; sourceTree = "<group>"; };
		357A6C7BC1E1B1456C3B944CC0AA7556 /* RemoteOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/RemoteOptions.swift; sourceTree = "<group>"; };
		35A615C7ADDD5851E814B5CF71FA1C99 /* LeaveCallConfirmationView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LeaveCallConfirmationView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/LeaveCallConfirmation/LeaveCallConfirmationView.swift; sourceTree = "<group>"; };
		35EB4DF2FFAC5624054E8EFC279C9DAA /* PushNotificationEventType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PushNotificationEventType.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/PushNotificationEventType.swift; sourceTree = "<group>"; };
		36029DB0C38EEBA6052EB14C8E6E52BE /* PushNotification.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PushNotification.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/PushNotification.swift; sourceTree = "<group>"; };
		******************************** /* PlatformInfoProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PlatformInfoProvider.swift; path = sdk/core/AzureCore/Source/Providers/PlatformInfoProvider.swift; sourceTree = "<group>"; };
		36A29891482E11A9ED17B985536ED272 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ru.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		36A403B0972A7E9C66FB5CADF435B3A3 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = fi.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		37B2217166ECA8A7E581A68CB86867C4 /* ErrorState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ErrorState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/ErrorState.swift; sourceTree = "<group>"; };
		37C9433A53B7FC888E96775D61B094DC /* ButtonViewData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonViewData.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/ButtonViewData.swift; sourceTree = "<group>"; };
		381B586F9BEBB3395D53891762198DC9 /* Pods-iOSProject-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProject-Info.plist"; sourceTree = "<group>"; };
		383D8B37BAA305E3CAC8AAECE10FC9EA /* RemoteParticipantsEventsAdapter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantsEventsAdapter.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/RemoteParticipantsEventsAdapter.swift; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		3891CEEBB5A4B8FFB9E0FA827E01AAAA /* NSLayoutConstraint+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSLayoutConstraint+Extensions.swift"; path = "ios/FluentUI/Extensions/NSLayoutConstraint+Extensions.swift"; sourceTree = "<group>"; };
		389CABF0F421788C4E7E1DCD4F5ABA50 /* SwipeActionModifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwipeActionModifier.swift; path = Sources/SwipeActions/ViewModifiers/SwipeActionModifier.swift; sourceTree = "<group>"; };
		38A559F770896C057085120739844D3E /* IncomingCall.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IncomingCall.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/IncomingCall.swift; sourceTree = "<group>"; };
		39095A9B9926B6694C02C86037E9A96C /* CallDiagnosticsViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallDiagnosticsViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Diagnostics/CallDiagnosticsViewModel.swift; sourceTree = "<group>"; };
		3992DC8F2E3FA6345E28F02D0C9B4979 /* MSFAvatar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MSFAvatar.swift; path = ios/FluentUI/Avatar/MSFAvatar.swift; sourceTree = "<group>"; };
		39E8996DDD47D8ADC59F0D5EB78254C5 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "pl-PL.lproj/Localizable.strings"; sourceTree = "<group>"; };
		3A1ABC86FF6BE6569A82921588B3E402 /* CommunicationTokenCredential.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationTokenCredential.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/CommunicationTokenCredential.swift; sourceTree = "<group>"; };
		3B096016243FC8A40EFCC8FDA5079DD5 /* CallComposite.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallComposite.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallComposite.swift; sourceTree = "<group>"; };
		3B0C855E6E711B3B54F4B8F1FD2B9718 /* Identifiers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Identifiers.swift; path = sdk/communication/AzureCommunicationCommon/Source/Identifiers.swift; sourceTree = "<group>"; };
		3B8B2050F4151E9A649065F3EB9D530F /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "fr-FR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		3BDB9A196109E2377F7B394312BA8411 /* CallingSDKWrapperProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingSDKWrapperProtocol.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/CallingSDKWrapperProtocol.swift; sourceTree = "<group>"; };
		3C0AAB679727109EC948F9C34DF367DF /* CaptionsLanguageListViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsLanguageListViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsLanguageListViewModel.swift; sourceTree = "<group>"; };
		3C31F64E6A2412601E3768516A40C307 /* AzureCodable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AzureCodable.swift; path = sdk/core/AzureCore/Source/Util/AzureCodable.swift; sourceTree = "<group>"; };
		3C36F6C6E5D1925404A3822BD531E6AA /* PermissionReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PermissionReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/PermissionReducer.swift; sourceTree = "<group>"; };
		3C443BA32FEECFFEA8748B4509FC245D /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = he.lproj/Localizable.strings; sourceTree = "<group>"; };
		3C4993DE15FB4CA19E6DF78F20D8B5D9 /* AzureCore.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AzureCore.modulemap; sourceTree = "<group>"; };
		3F5B263FAB571222869EB06710AFB542 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		40502CAA76933496B20D68093A435F06 /* CallScreenHeaderViewData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenHeaderViewData.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallScreenHeaderViewData.swift; sourceTree = "<group>"; };
		4087E556F3E09E569E812BC87B799984 /* LocalUserReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalUserReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/LocalUserReducer.swift; sourceTree = "<group>"; };
		408A3AE5333ED59ACF32EC58DBC49E03 /* AudioSessionState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioSessionState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/AudioSessionState.swift; sourceTree = "<group>"; };
		410855F1FB1F9DD85D16DCE13B7562C1 /* ACSVideoStreamRendererView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSVideoStreamRendererView.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h"; sourceTree = "<group>"; };
		410EC546636B376A22B33FB306BEB539 /* Pods-iOSProject-iOSProjectUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProject-iOSProjectUITests.release.xcconfig"; sourceTree = "<group>"; };
		416555BCA7BF7A22F59E7E19BF44833F /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ja.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		419FF83B5DE08258908F2BF54EA6B771 /* MicrosoftFluentUI-FluentUIResources-ios */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "MicrosoftFluentUI-FluentUIResources-ios"; path = "FluentUIResources-ios.bundle"; sourceTree = BUILT_PRODUCTS_DIR; };
		421BAA4C8B9F1E47B66491B84536D33D /* Persona.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Persona.swift; path = ios/FluentUI/Avatar/Persona.swift; sourceTree = "<group>"; };
		42318D37CA282796B604ABA8F9095C0C /* AppLifeCycleState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppLifeCycleState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/AppLifeCycleState.swift; sourceTree = "<group>"; };
		42332C34304DD69946E9B7CAE443E868 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		425529FC72F1D78C2518CD971694D55D /* AzureCommunicationUICalling.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationUICalling.release.xcconfig; sourceTree = "<group>"; };
		4279AAB381C0731002968A5C15E82F35 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		4284C28FAE7380E750A92A818ADD0B2C /* CommunicationAccessToken.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationAccessToken.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/CommunicationAccessToken.swift; sourceTree = "<group>"; };
		4352CBF9DBC48AF71723F338BAD43CDD /* ControlBarViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ControlBarViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/ControlBarViewModel.swift; sourceTree = "<group>"; };
		43B49E5D2505CBF81E0779096790AE01 /* JwtTokenParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JwtTokenParser.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/JwtTokenParser.swift; sourceTree = "<group>"; };
		43F1F53555D656DD229B1B798CABEA87 /* LoadingOverlayViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LoadingOverlayViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/LoadingOverlayViewModel.swift; sourceTree = "<group>"; };
		4423E83C671B759C5512539D57237C05 /* RequestIdPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RequestIdPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/RequestIdPolicy.swift; sourceTree = "<group>"; };
		44C69948F42ADCBF5C82B5B7AA3E4012 /* ParticipantGridViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/ParticipantGridViewModel.swift; sourceTree = "<group>"; };
		456791E520F7D5F02377D1C80E535E15 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = en.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		45BCD08F067AB985DD78CBCD4E749A2B /* SwiftUI+ViewModifiers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "SwiftUI+ViewModifiers.swift"; path = "ios/FluentUI/Core/SwiftUI+ViewModifiers.swift"; sourceTree = "<group>"; };
		470E3141B7AF49108835912477BE1C6C /* ParticipantGridCellVideoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridCellVideoView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/Cell/ParticipantGridCellVideoView.swift; sourceTree = "<group>"; };
		479B603E5C8EAD405D4DCA6344A9E171 /* MicrosoftFluentUI */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MicrosoftFluentUI; path = FluentUI.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		47DF492CCAC5D5667C4D1ADBAB7C6D38 /* LandscapeAwareKeyboardWatcher.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LandscapeAwareKeyboardWatcher.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Utilities/LandscapeAwareKeyboardWatcher.swift; sourceTree = "<group>"; };
		483EB492B527B21DE34EA5A33C1C0719 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = cs.lproj/Localizable.strings; sourceTree = "<group>"; };
		488F28FA0B8F2A146DDB19B952EE5EF3 /* FluentUI-ios.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = "FluentUI-ios.xcassets"; path = "ios/FluentUI/Resources/FluentUI-ios.xcassets"; sourceTree = "<group>"; };
		4942C9B87F243C0F84EF40A55FD95C73 /* AzureCommunicationCalling.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationCalling.release.xcconfig; sourceTree = "<group>"; };
		49C58831712EB325A9DB3AEA7E2CC6BA /* CapabilitiesChangedEvent.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapabilitiesChangedEvent.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CapabilitiesChangedEvent.swift; sourceTree = "<group>"; };
		49CFB64DADC6099E722D28E4E4DCCAB1 /* DimmingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DimmingView.swift; path = ios/FluentUI/Obscurable/DimmingView.swift; sourceTree = "<group>"; };
		4AA4D55C12E766693D1C1C61EBFD5857 /* Pods-iOSProject-iOSProjectUITests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProject-iOSProjectUITests-acknowledgements.plist"; sourceTree = "<group>"; };
		4AC2A6809879809D48466875C2616841 /* ParticipantMenuView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantMenuView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ParticipantActions/ParticipantMenuView.swift; sourceTree = "<group>"; };
		4B951D8B6D4294F7E0A70D7BFCF0CEDA /* LeaveCallConfirmationViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LeaveCallConfirmationViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/LeaveCallConfirmation/LeaveCallConfirmationViewModel.swift; sourceTree = "<group>"; };
		4C096B3D9CE8B83F8C5468C5FAB40A6D /* LocalUserAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalUserAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/LocalUserAction.swift; sourceTree = "<group>"; };
		4C253A2EB0B95B3FD1062CED3E36E95D /* Pods-iOSProjectTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-iOSProjectTests-dummy.m"; sourceTree = "<group>"; };
		4C61322F803159EF8E0006CA32C8D4ED /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		4CA8D88AB94D8AD39F264A7F56D2A884 /* LobbyOverlayViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyOverlayViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/LobbyOverlayViewModel.swift; sourceTree = "<group>"; };
		4D1CE550E0EA8ECCE19FD48113D7B9A3 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = uk.lproj/Localizable.strings; sourceTree = "<group>"; };
		4DBF4F4963BAAE6B33AC384CA7A7B51E /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "he-IL.lproj/Localizable.strings"; sourceTree = "<group>"; };
		4E281BD3A0DD63139190CA6BD921C536 /* UserFacingDiagnosticModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UserFacingDiagnosticModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/UserFacingDiagnosticModel.swift; sourceTree = "<group>"; };
		4E8E6731BD3F7A9E606599ACFDAA1C5E /* UIImage+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIImage+Extensions.swift"; path = "ios/FluentUI/Extensions/UIImage+Extensions.swift"; sourceTree = "<group>"; };
		4E95D969C014886926D36D05A1028A7D /* CallCompositeError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeError.swift; sourceTree = "<group>"; };
		4EC4254B28BB6C1CF9A2A313350DD2F3 /* TableViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableViewCell.swift; path = "ios/FluentUI/Table View/TableViewCell.swift"; sourceTree = "<group>"; };
		4F88B9F46FDE2365645E2076AA1AC802 /* ThemeOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ThemeOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/ThemeOptions.swift; sourceTree = "<group>"; };
		4FF705F9C575520FF9FD76F5C881C6DC /* AzureCommunicationUICalling-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCommunicationUICalling-umbrella.h"; sourceTree = "<group>"; };
		50254538F3951872CDAFF851D017119D /* TokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TokenSet.swift; path = ios/FluentUI/Core/Theme/Tokens/TokenSet.swift; sourceTree = "<group>"; };
		50DCAD23FC62D0A29CFD2593BA42E2DA /* StringConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StringConstants.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/StringConstants.swift; sourceTree = "<group>"; };
		51853927E49C4E82CA25617521D8688F /* LobbyErrorHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyErrorHeaderView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/LobbyErrorHeaderView.swift; sourceTree = "<group>"; };
		519C152346F424BA0E5F1C4BC22965D9 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "fi-FI.lproj/Localizable.strings"; sourceTree = "<group>"; };
		531EA0E8D01F61729B3C499E45B3840E /* AccessibilityProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AccessibilityProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/AccessibilityProvider.swift; sourceTree = "<group>"; };
		53A1A5BCE00C1B6C8FA9ADD27AC44D58 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		53C702B517D23933A9D72B85B441E008 /* AzureCommunicationCalling.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AzureCommunicationCalling.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h"; sourceTree = "<group>"; };
		5437291470A18CFC7EE83A02866CFAED /* AzureTask.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AzureTask.swift; path = sdk/core/AzureCore/Source/AzureTask.swift; sourceTree = "<group>"; };
		544338A618970DEA569F4C6A0A511B33 /* DebugInfoManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DebugInfoManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/DebugInfoManager.swift; sourceTree = "<group>"; };
		55119FCF266D0C9EBFBB240A6D46C87C /* PrimaryButtonViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PrimaryButtonViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/PrimaryButtonViewModel.swift; sourceTree = "<group>"; };
		555C43BF893F4A3B9EB64C35B2A7CD9C /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = pl.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		55E6A5A541EDA812FA06386F0549BB33 /* ThemeColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ThemeColor.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/ThemeColor.swift; sourceTree = "<group>"; };
		5633990505F9B3ADD75173ED25153A26 /* Pods-iOSProjectTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProjectTests.release.xcconfig"; sourceTree = "<group>"; };
		56421BBE1E308F6C975EF9D76BE8B499 /* StaticTokenCredential.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StaticTokenCredential.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/StaticTokenCredential.swift; sourceTree = "<group>"; };
		56BAC2316328AEF2D1C004A275517C81 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = tr.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		56CBA3E5071759DB2C12C47E68A7D883 /* AudioSessionAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioSessionAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/AudioSessionAction.swift; sourceTree = "<group>"; };
		57531BACE40433640AE941D2E9A63956 /* MicrosoftFluentUI-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MicrosoftFluentUI-umbrella.h"; sourceTree = "<group>"; };
		57DD7FD9EFDC59139E768AC3F545C3F0 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = fi.lproj/Localizable.strings; sourceTree = "<group>"; };
		57FE731BE327AC64D43677BAA6B299CB /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		5863B19854CB996EEC6B3CE4C2F6EED6 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ca.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		5872E993C44A65010BE1315D3960CF5A /* AzureCore-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AzureCore-dummy.m"; sourceTree = "<group>"; };
		58732D1B29C41808E4C37629F8FBAFB0 /* CallingState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/CallingState.swift; sourceTree = "<group>"; };
		58AE038C337B203EB262A041A02C5534 /* SetupControlBarViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetupControlBarViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/SetupControlBarViewModel.swift; sourceTree = "<group>"; };
		59146327A33EEE4EC4A144BB2D95B04D /* PopupMenuController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuController.swift; path = "ios/FluentUI/Popup Menu/PopupMenuController.swift"; sourceTree = "<group>"; };
		594E9A3763EF54330E716784A1E0D5F1 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		596F9D63F60626040AEC01E09924B2CA /* CompositeAvatar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeAvatar.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/FluentUI/Wrapper/CompositeAvatar.swift; sourceTree = "<group>"; };
		59B48080D1EFD51F65E3049A052FA1FB /* TokenizedControlView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TokenizedControlView.swift; path = ios/FluentUI/Core/Theme/Tokens/TokenizedControlView.swift; sourceTree = "<group>"; };
		59F3D5210DA73FA0305605C65598A66C /* JoiningCallActivityViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JoiningCallActivityViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/JoiningCallActivityViewModel.swift; sourceTree = "<group>"; };
		5A14D44B69885CB8904DC3328DE82C75 /* MicrosoftFluentUI-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MicrosoftFluentUI-prefix.pch"; sourceTree = "<group>"; };
		5A563B17F12AA0B0DC4D1E5C448A1236 /* ControlBarView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ControlBarView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/ControlBarView.swift; sourceTree = "<group>"; };
		5A805D016E85F90354680EFC21A160FF /* Avatar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Avatar.swift; path = ios/FluentUI/Avatar/Avatar.swift; sourceTree = "<group>"; };
		5A962D257B2DCC49DBA3298654A61BD3 /* PipelineRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineRequest.swift; path = sdk/core/AzureCore/Source/Pipeline/PipelineRequest.swift; sourceTree = "<group>"; };
		5ADCEA4BD476BAC9E7D27760C472A3F5 /* PopupMenuProtocols.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuProtocols.swift; path = "ios/FluentUI/Popup Menu/PopupMenuProtocols.swift"; sourceTree = "<group>"; };
		5B5BEFFA3B0DA6976BC05D13CB8AAA62 /* CallCompositeCaptionsData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeCaptionsData.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CallCompositeCaptionsData.swift; sourceTree = "<group>"; };
		5C2ECC3C94603626E6A959FC467460AD /* Label.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Label.swift; path = ios/FluentUI/Label/Label.swift; sourceTree = "<group>"; };
		5CB2963F6976CB6B1583C80139DADA21 /* RemoteParticipantExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/RemoteParticipantExtension.swift; sourceTree = "<group>"; };
		5CD6AE56E1F1FEA7A07966D3AC952D93 /* LocalVideoStreamExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalVideoStreamExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/LocalVideoStreamExtension.swift; sourceTree = "<group>"; };
		5DD289A3EB5470C361F1E553F4B91AD3 /* DrawerViewControllerProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerViewControllerProtocol.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/FluentUI/Wrapper/DrawerViewControllerProtocol.swift; sourceTree = "<group>"; };
		5E6E68698D479A4417ACEF986F8120CB /* CapabilitiesManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapabilitiesManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CapabilitiesManager.swift; sourceTree = "<group>"; };
		5E835ED93C3C8CFF613B0201B069C57E /* MicrosoftFluentUI.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MicrosoftFluentUI.debug.xcconfig; sourceTree = "<group>"; };
		5ECE18B19EFA2A761E3CB028CE0B500C /* SetupViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetupViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewModel.swift; sourceTree = "<group>"; };
		5F3ABC536DA58980997E4C4E10F27242 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = he.lproj/Localizable.strings; sourceTree = "<group>"; };
		5F9E74FAE941FA03F3D71F803B2F2551 /* IconButtonViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconButtonViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/IconButtonViewModel.swift; sourceTree = "<group>"; };
		5FC4615733668365907B297184444CD4 /* AutoRefreshTokenCredential.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AutoRefreshTokenCredential.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/AutoRefreshTokenCredential.swift; sourceTree = "<group>"; };
		5FD46B21C237D0FE9D8F321DB739267A /* AzureCommunicationCommon.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationCommon.release.xcconfig; sourceTree = "<group>"; };
		5FE547F9EF02B7E6F687256F19E8E695 /* PermissionState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PermissionState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/PermissionState.swift; sourceTree = "<group>"; };
		60E48F5F2991D7AFAD34036D67BC593B /* UIColor+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIColor+Extensions.swift"; path = "ios/FluentUI/Extensions/UIColor+Extensions.swift"; sourceTree = "<group>"; };
		60EB9D9E66A3F6F6812B402F23CF6D72 /* CaptionsRttDataManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttDataManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CaptionsRttDataManager.swift; sourceTree = "<group>"; };
		6135E9D116F377CA1936C39AC919E014 /* LifecycleAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LifecycleAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/LifecycleAction.swift; sourceTree = "<group>"; };
		617DA3CBB02D35BAC9844F8D6FDE5AF0 /* DrawerSelectableItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerSelectableItemView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/DrawerSelectableItemView.swift; sourceTree = "<group>"; };
		61ADA42FF52C12B03D85EB219A9664F5 /* Pods-iOSProjectTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProjectTests-acknowledgements.plist"; sourceTree = "<group>"; };
		62755A819E1320B8A63CEA61C0EC9153 /* ConvertingInitializers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConvertingInitializers.swift; path = sdk/core/AzureCore/Source/Util/ConvertingInitializers.swift; sourceTree = "<group>"; };
		636F88A1B58EFDD74E5FBDD6AA7867F2 /* AzureDate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AzureDate.swift; path = sdk/core/AzureCore/Source/DataStructures/AzureDate.swift; sourceTree = "<group>"; };
		6531966EFBFDC7768369081A621DE586 /* ColorThemeProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ColorThemeProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Style/ColorThemeProvider.swift; sourceTree = "<group>"; };
		6531D67EE076410ADD78162639954F4C /* CaptionsErrorView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsErrorView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsErrorView.swift; sourceTree = "<group>"; };
		653F2725768132F7554E94A593C38EFA /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "en-GB.lproj/Localizable.strings"; sourceTree = "<group>"; };
		65BF1421F6F1F18A74621DBF779AAA44 /* PipelineClient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineClient.swift; path = sdk/core/AzureCore/Source/Pipeline/PipelineClient.swift; sourceTree = "<group>"; };
		66B83E0A984344AF9E317FECA71A1DEC /* LocalVideoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalVideoViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/VideoView/LocalVideoViewModel.swift; sourceTree = "<group>"; };
		673643236C52F7E2127858CE0D342842 /* OnHoldOverlayViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OnHoldOverlayViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/OnHoldOverlayViewModel.swift; sourceTree = "<group>"; };
		6785AF3A16342D48B349E70A64F54E84 /* Pods-iOSProject-iOSProjectUITests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-iOSProject-iOSProjectUITests"; path = Pods_iOSProject_iOSProjectUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		684829E58B8B328DFBB2C1CB8B1BC2E6 /* PipReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/PipReducer.swift; sourceTree = "<group>"; };
		6860143A03DE7807AF0E4BC47E73FA00 /* PreferenceKey.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreferenceKey.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/PreferenceKey.swift; sourceTree = "<group>"; };
		68E593E829379139F55496F13110A628 /* AvatarGroupTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarGroupTokenSet.swift; path = ios/FluentUI/AvatarGroup/AvatarGroupTokenSet.swift; sourceTree = "<group>"; };
		69A1C089943204C4AE2DAC38A62D12AA /* LocalVideoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalVideoView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/VideoView/LocalVideoView.swift; sourceTree = "<group>"; };
		69F87B3A7FD09BB690412D6EC10FF1C8 /* RemoteParticipantsReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantsReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/RemoteParticipantsReducer.swift; sourceTree = "<group>"; };
		6A52F633C7D0F66B3802222AECCF3651 /* DeviceExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DeviceExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/DeviceExtension.swift; sourceTree = "<group>"; };
		6A96D349B46E3A2E79875386F0B61590 /* AzureCommunicationCalling-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "AzureCommunicationCalling-xcframeworks.sh"; sourceTree = "<group>"; };
		6AD3746AC595789F2E09556DB5BC075E /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "ru-RU.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6AFA5583BAA14DCA4B53A4665A015A39 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "pt-PT.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		6B1DF1EF3DB9D83F0114F0D5CA7D7E97 /* RemoteParticipantsManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantsManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/RemoteParticipantsManager.swift; sourceTree = "<group>"; };
		6B670DC714CFE9043492068C3CD54B8E /* MicrosoftFluentUI.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MicrosoftFluentUI.release.xcconfig; sourceTree = "<group>"; };
		6C06ECB67B0B0C5AA3FC10215D17D7D2 /* Pods-iOSProject-iOSProjectUITests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-iOSProject-iOSProjectUITests-dummy.m"; sourceTree = "<group>"; };
		6C1FA818AB4C90CA525B8452FE461345 /* PopupMenuItemCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuItemCell.swift; path = "ios/FluentUI/Popup Menu/PopupMenuItemCell.swift"; sourceTree = "<group>"; };
		6C60F13B32E165751CA8F4718EA71D2A /* SupportFormViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SupportFormViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/SupportForm/SupportFormViewModel.swift; sourceTree = "<group>"; };
		6C92BEB6651D97E4A6A379DD09193612 /* VideoStreamInfoModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoStreamInfoModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/VideoStreamInfoModel.swift; sourceTree = "<group>"; };
		6D1C4DCEE12252D7285D284F6FD69114 /* CallError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallError.swift; sourceTree = "<group>"; };
		6D5130D0FB0CEBC82210A8F669CD58F6 /* CommunicationTokenCredentialError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationTokenCredentialError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CommunicationTokenCredentialError.swift; sourceTree = "<group>"; };
		6D6A1B022215268BD3A32D50A6BE28AE /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = el.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		6E1BFAE51577B8C3CB22F6A9CE2C8F1B /* SwipeActions.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SwipeActions.release.xcconfig; sourceTree = "<group>"; };
		6E31637A3AC15FC5289F1CE3FCE2BC7A /* VideoRenderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoRenderView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/VideoView/VideoRenderView.swift; sourceTree = "<group>"; };
		6E9435372F287B5673FBBAB771DEB5B1 /* MessageBarDiagnosticView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MessageBarDiagnosticView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Diagnostics/MessageBarDiagnosticView.swift; sourceTree = "<group>"; };
		6EF0DE753E0DF42EFB0DE21A97313535 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "pt-BR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6F3940DD8AE1F12F6138213A990CCD7C /* CallScreenOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallScreenOptions.swift; sourceTree = "<group>"; };
		708438BC86FAA596A5EA9E36EF56148A /* CallCompositeAudioVideoMode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeAudioVideoMode.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeAudioVideoMode.swift; sourceTree = "<group>"; };
		70C47B69E283EEB360F4B72D1B78E09B /* CallingSDKEventsHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingSDKEventsHandler.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/CallingSDKEventsHandler.swift; sourceTree = "<group>"; };
		70F84D3362118FDE5C4F28877FDF3ADB /* CallingAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/CallingAction.swift; sourceTree = "<group>"; };
		713AE5DD6CF53E0A12CA3B0630A402E7 /* Pods-iOSProject-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-iOSProject-umbrella.h"; sourceTree = "<group>"; };
		716A169760385FB8F339D36EDAD9F6F3 /* Pods-iOSProjectTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-iOSProjectTests"; path = Pods_iOSProjectTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		71F4AAB750FB164B117EFD1F78D4AA9D /* AccessibilityProviderNotificationsObserver.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AccessibilityProviderNotificationsObserver.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/AccessibilityProviderNotificationsObserver.swift; sourceTree = "<group>"; };
		72277D2220FD0CC7547C22183F0C6EDF /* BundleInfoProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BundleInfoProvider.swift; path = sdk/core/AzureCore/Source/Providers/BundleInfoProvider.swift; sourceTree = "<group>"; };
		72937CDD4EA795451A56FA0099CACA01 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = th.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		72BA9B572A2DAA9B61329A163AC62344 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = th.lproj/Localizable.strings; sourceTree = "<group>"; };
		72E11C4258EA6DFAB8A1CC19A677796A /* MappedSequence.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MappedSequence.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/MappedSequence.swift; sourceTree = "<group>"; };
		738B9D5CFD1A8F6FB96730CB4C78AC83 /* String+Extension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "String+Extension.swift"; path = "ios/FluentUI/Extensions/String+Extension.swift"; sourceTree = "<group>"; };
		742D945200D910034338D83A01229324 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = hi.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		74B299A2D09F2578C65AB73C731B5763 /* CaptionsAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/CaptionsAction.swift; sourceTree = "<group>"; };
		74EAFBA1A72000C286263A66C76F2AC6 /* ActivityIndicatorTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ActivityIndicatorTokenSet.swift; path = ios/FluentUI/ActivityIndicator/ActivityIndicatorTokenSet.swift; sourceTree = "<group>"; };
		7585D83C3D2E9F1E0B576CB6095C0EE7 /* VideoViewManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoViewManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/VideoViewManager.swift; sourceTree = "<group>"; };
		75B10E1058C54DF613C799D1C9E2C464 /* CallingSDKWrapper.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingSDKWrapper.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/CallingSDKWrapper.swift; sourceTree = "<group>"; };
		75FA895C9FED615162E47E559ED70D68 /* AzureCommunicationCommon.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AzureCommunicationCommon.modulemap; sourceTree = "<group>"; };
		7724B3604CF20668E6B53313DDC88CF3 /* InfoHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InfoHeaderView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/InfoHeaderView.swift; sourceTree = "<group>"; };
		78777E17C95C21DFAF5FA16332036A41 /* FontInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FontInfo.swift; path = ios/FluentUI/Core/Theme/Tokens/FontInfo.swift; sourceTree = "<group>"; };
		791BF17049398C5A57139824CDDE8676 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "pt-PT.lproj/Localizable.strings"; sourceTree = "<group>"; };
		79C7C977DC9B706F740B9806B6DA88FD /* HTTPResponse.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTTPResponse.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/HTTPResponse.swift; sourceTree = "<group>"; };
		7A1C9C94BEC5608D7DE305E4180822A0 /* ControlTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ControlTokenSet.swift; path = ios/FluentUI/Core/Theme/Tokens/ControlTokenSet.swift; sourceTree = "<group>"; };
		7A591602F63815725832B77BDEA394BD /* ACSCallKit.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSCallKit.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/ACSCallKit.h"; sourceTree = "<group>"; };
		7A9A6D03F74274EE6AEDBF0CB25CA7A9 /* IncomingCallCancelled.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IncomingCallCancelled.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/IncomingCallCancelled.swift; sourceTree = "<group>"; };
		7B0BF5CF531B79E2A1F160893249238E /* CompositeViewModelFactory.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeViewModelFactory.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Factories/CompositeViewModelFactory.swift; sourceTree = "<group>"; };
		7B2BDF2CB10073A7997A16204BDD133D /* LobbyWaitingHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyWaitingHeaderView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/LobbyWaitingHeaderView.swift; sourceTree = "<group>"; };
		7B6FFBE2E8EBA1794FB724F48964DB51 /* ThrottleMiddleware.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ThrottleMiddleware.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Middleware/ThrottleMiddleware.swift; sourceTree = "<group>"; };
		7BC176FA5AFAF40683ABDABF415FB721 /* BottomToastViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BottomToastViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ToastNotification/BottomToastViewModel.swift; sourceTree = "<group>"; };
		7C613D393C7137E783637A2A89D73B87 /* CaptionsRttListViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttListViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsRttListViewModel.swift; sourceTree = "<group>"; };
		7DC7F22CF13B575C14DC2843CAD51A7A /* CancelBag.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CancelBag.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/CancelBag.swift; sourceTree = "<group>"; };
		7DFFCEB2678C46FEB76ECA7B86C58867 /* CallCompositeInternalError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeInternalError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/CallCompositeInternalError.swift; sourceTree = "<group>"; };
		7E6942AC18DB9B758098A0C2AF30DE35 /* ParticipantListVIew.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantListVIew.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ParticipantsList/ParticipantListVIew.swift; sourceTree = "<group>"; };
		7E78741BE32DB058AE93ADCCBA6948CA /* FluentUIFramework.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FluentUIFramework.swift; path = ios/FluentUI/Core/FluentUIFramework.swift; sourceTree = "<group>"; };
		7E7C148B1A4DB6A6C6682CAA4E95EDF3 /* ACSCallKit.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSCallKit.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSCallKit.h"; sourceTree = "<group>"; };
		7EEC83AA5B34B5CA3012B756A68737FB /* CompositeExitManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeExitManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/CompositeExitManager.swift; sourceTree = "<group>"; };
		7EF2FD88407950F3C079382A97AE767E /* DrawerParticipantView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerParticipantView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/DrawerParticipantView.swift; sourceTree = "<group>"; };
		7EF3EB30D6621EE0834EAE4441AF795A /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = nl.lproj/Localizable.strings; sourceTree = "<group>"; };
		7F445959FCF588BB44F78248770A3B35 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = sv.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		7F452CF9330FB3E3E5A3427CD68B893E /* BannerViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BannerViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Banner/BannerViewModel.swift; sourceTree = "<group>"; };
		7F722929799BB6727D9F120813E18DC5 /* MeasureSizeModifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MeasureSizeModifier.swift; path = Sources/SwipeActions/ViewModifiers/MeasureSizeModifier.swift; sourceTree = "<group>"; };
		801D77A0AAC9D93241C56A9E090A21CA /* ReachabilityManagerType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ReachabilityManagerType.swift; path = sdk/core/AzureCore/Source/Util/ReachabilityManagerType.swift; sourceTree = "<group>"; };
		80FC77A08DA4DF9314E25B83FB4EAC7F /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "nb-NO.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		8258DFFB1E94835CC3E87FE47CC21530 /* ShadowInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ShadowInfo.swift; path = ios/FluentUI/Core/Theme/Tokens/ShadowInfo.swift; sourceTree = "<group>"; };
		82B588EE471DD4863C4F3E1827B75F16 /* PreviewAreaView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewAreaView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/PreviewAreaView.swift; sourceTree = "<group>"; };
		82EF81BA07C5A8CCD7C478E6326FCC03 /* LoadingOverlayView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LoadingOverlayView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/LoadingOverlayView.swift; sourceTree = "<group>"; };
		83E02E02FB925819D7301762052BCC4A /* Pods-iOSProject-iOSProjectUITests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-iOSProject-iOSProjectUITests-umbrella.h"; sourceTree = "<group>"; };
		84BB5B98C93B913663B1A4BF3E5D9016 /* DebugInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DebugInfo.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/DebugInfo.swift; sourceTree = "<group>"; };
		852CB6DF7B4D54E2BB7CC8B4BBB94717 /* StoreExtensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StoreExtensions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/StoreExtensions.swift; sourceTree = "<group>"; };
		8633FC83636AAFA95C3E5202191FC60A /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = da.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		86A6D8888B34A734A9E122615D138F72 /* UIApplication+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIApplication+Extensions.swift"; path = "ios/FluentUI/Extensions/UIApplication+Extensions.swift"; sourceTree = "<group>"; };
		873D32D3C85C8CF0AAA3E6429F065BF1 /* DrawerController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerController.swift; path = ios/FluentUI/Drawer/DrawerController.swift; sourceTree = "<group>"; };
		878629BA144808F20AA70C561DA96B78 /* HTTPHeader.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTTPHeader.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/HTTPHeader.swift; sourceTree = "<group>"; };
		87D3BB8D380E82C2DC20476E49EC67BA /* OrientationOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OrientationOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/OrientationOptions.swift; sourceTree = "<group>"; };
		87EC8CD373B0D2683C8B40EFAFDBE3B1 /* XMLTree.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = XMLTree.swift; path = sdk/core/AzureCore/Source/DataStructures/XMLTree.swift; sourceTree = "<group>"; };
		886D76936C176D59BE4546B272852002 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = hu.lproj/Localizable.strings; sourceTree = "<group>"; };
		8996DBCF82707F4C5F1314D6B1E64263 /* Pods-iOSProject-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSProject-acknowledgements.plist"; sourceTree = "<group>"; };
		89E6D13DD0567DF3585578CE6EBEB112 /* RttReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RttReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/RttReducer.swift; sourceTree = "<group>"; };
		8A1C1AE8A53E82FBBBE179D3147C2E2C /* PermissionsManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PermissionsManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/PermissionsManager.swift; sourceTree = "<group>"; };
		8A207EDDB62D6F12026376F41C1695D4 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "en-US.lproj/Localizable.strings"; sourceTree = "<group>"; };
		8AC9318696C94A812476415CD384D100 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ro.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		8BABD6164944B7C7CDCF83DC121E7C4E /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		8C9AB15AEFE48DF18268688F4459844F /* BannerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BannerView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Banner/BannerView.swift; sourceTree = "<group>"; };
		8C9D31FF9ADCCA290A6255170F20A809 /* AzureCommunicationUICalling-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AzureCommunicationUICalling-dummy.m"; sourceTree = "<group>"; };
		8CACDB86BE30C1B24E0BCAD244C93DF0 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "sv-SE.lproj/Localizable.strings"; sourceTree = "<group>"; };
		8CEF10B711B3B51BCCD4B22AD92714D6 /* UIViewControllerExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UIViewControllerExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/UIViewControllerExtension.swift; sourceTree = "<group>"; };
		8D2A393468C1F2FFCE47A211112EDE4E /* CallingMiddlewareHandlerExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingMiddlewareHandlerExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Middleware/CallingMiddlewareHandlerExtension.swift; sourceTree = "<group>"; };
		8D8EAC68E816B1C69E0AE28EE2C391C7 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = hr.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		8E89A1B2DEFEE30239E7A7CBE73CB400 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "tr-TR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		8EC41BB5A297CCB29ABF141D36B5D67A /* SwiftUI+ViewPresentation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "SwiftUI+ViewPresentation.swift"; path = "ios/FluentUI/Core/SwiftUI+ViewPresentation.swift"; sourceTree = "<group>"; };
		8EDFA7A885D3AE403DF6AAD89DC57623 /* CallingReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/CallingReducer.swift; sourceTree = "<group>"; };
		8FD7D688A01765E4FD6E4D9710EB9E15 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = sv.lproj/Localizable.strings; sourceTree = "<group>"; };
		8FF3887B3B601C3932BE9AA47D95F7D2 /* EnvironmentValuesExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EnvironmentValuesExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/EnvironmentValuesExtension.swift; sourceTree = "<group>"; };
		900549E16FD6D5016483CD194733DE86 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = it.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		90D2121BD58D81957CE8795045BD56E5 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ms.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		90D50155493B61A0569090870A2135DC /* JoiningCallActivityView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JoiningCallActivityView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/JoiningCallActivityView.swift; sourceTree = "<group>"; };
		90F5FB9F4C5A96A44C1C62CED232C492 /* ACSVideoStreamRendererView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSVideoStreamRendererView.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h"; sourceTree = "<group>"; };
		91A315E48F79E42026749857DF9D40FB /* AddDatePolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AddDatePolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/AddDatePolicy.swift; sourceTree = "<group>"; };
		91E544D27ECBE7743545D0E4D38D108A /* Pods-iOSProject-iOSProjectUITests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-iOSProject-iOSProjectUITests.modulemap"; sourceTree = "<group>"; };
		91E7ABAEF8120BD170CA30ACFC5D8F1B /* ParticipantGridLayoutView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridLayoutView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/ParticipantGridLayoutView.swift; sourceTree = "<group>"; };
		9275DCC7B88C1F257EFB7A5BC59AFFF2 /* Pods-iOSProject.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProject.debug.xcconfig"; sourceTree = "<group>"; };
		93C650BBF867FEDC8E4467E54E7607AC /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		9404205354FF9A7223C96D0934A51ED7 /* NormalizeETagPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NormalizeETagPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/NormalizeETagPolicy.swift; sourceTree = "<group>"; };
		941AFE87345A1A2B2FE73F2F5141B784 /* PipelineStage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineStage.swift; path = sdk/core/AzureCore/Source/Pipeline/PipelineStage.swift; sourceTree = "<group>"; };
		941D6002A365A05FA76FD7816D84CA06 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = el.lproj/Localizable.strings; sourceTree = "<group>"; };
		943A8EDFF888C1E9C1DF659C2A972A46 /* Store.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Store.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/Store.swift; sourceTree = "<group>"; };
		9450C7EF18073872DF8B0BF20E01AF74 /* NetworkManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NetworkManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/NetworkManager.swift; sourceTree = "<group>"; };
		949699B2A0BE63450DD8759B83341BAB /* AzureCore-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AzureCore-Info.plist"; sourceTree = "<group>"; };
		95042D657EC783F2B920241557CC2247 /* CallingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingView.swift; sourceTree = "<group>"; };
		95C2200421A2DC6122060706B419D7AE /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "es-ES.lproj/Localizable.strings"; sourceTree = "<group>"; };
		95C6FA52F3EA47D073C9052E26172911 /* DebugInfoSharingActivityViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DebugInfoSharingActivityViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/SharingActivity/DebugInfoSharingActivityViewModel.swift; sourceTree = "<group>"; };
		95F4C75EFEE51C98C333DE6B870DE5AB /* LeaveCallConfirmationMode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LeaveCallConfirmationMode.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/LeaveCallConfirmationMode.swift; sourceTree = "<group>"; };
		95FF4EA8F555AE41A62D8405BB04FD5D /* SwipeActions */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SwipeActions; path = SwipeActions.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		96186C0BC9C5EC5F394EF114B22D0F9B /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		967F0E039F8EB390364AD9B5F3A2749B /* CallingViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewModel.swift; sourceTree = "<group>"; };
		972E6D3F0789D216CB19876FD3D3E78D /* AzureCommunicationCalling.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AzureCommunicationCalling.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h"; sourceTree = "<group>"; };
		97B42ED0F4919871EE735A996465EFB2 /* ExpandableDrawer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpandableDrawer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/ExpandableDrawer.swift; sourceTree = "<group>"; };
		98628DE2E3D3A0E442AA61B799429A8D /* ToastNotificationAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ToastNotificationAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/ToastNotificationAction.swift; sourceTree = "<group>"; };
		9882C331297D666DF83612C2A9BC4337 /* OrientationProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OrientationProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Style/OrientationProvider.swift; sourceTree = "<group>"; };
		9888C41B7DD150EC19D9EEFF14FC5631 /* AvatarGroup.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarGroup.swift; path = ios/FluentUI/AvatarGroup/AvatarGroup.swift; sourceTree = "<group>"; };
		989E4D12CE4C5DAD41D76D4F9C90111B /* CommunicationTokenRefreshOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationTokenRefreshOptions.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/CommunicationTokenRefreshOptions.swift; sourceTree = "<group>"; };
		99330688314D8B66BD7D2087C17C1B3E /* CallingMiddleware.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingMiddleware.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Middleware/CallingMiddleware.swift; sourceTree = "<group>"; };
		998E99C627AA67517378F54138BAEC82 /* Logger.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Logger.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/Logger.swift; sourceTree = "<group>"; };
		99C09043F87A7021A9829A31FE06303D /* ToastNotificationState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ToastNotificationState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/ToastNotificationState.swift; sourceTree = "<group>"; };
		9A71F69DDEA462A85DE3C9B58C0693ED /* ACSCameraFacingExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ACSCameraFacingExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/ACSCameraFacingExtension.swift; sourceTree = "<group>"; };
		9A92C037CBCD0B65A68610A1397E6ECB /* CallScreenInfoHeaderReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenInfoHeaderReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/CallScreenInfoHeaderReducer.swift; sourceTree = "<group>"; };
		9B5B8D09A6C133E60E88624CC65F2635 /* ControlHostingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ControlHostingView.swift; path = ios/FluentUI/Core/ControlHostingView.swift; sourceTree = "<group>"; };
		9B8A85DC1E10B66D2B686119035414AE /* RequestString.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RequestString.swift; path = sdk/core/AzureCore/Source/Util/RequestString.swift; sourceTree = "<group>"; };
		9B8E9241C0858619F232FA308E9B4BDC /* Errors.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Errors.swift; path = sdk/core/AzureCore/Source/Errors.swift; sourceTree = "<group>"; };
		9BBE34CC70AFD621A825E6A45E886DE3 /* OverlayViewModelProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OverlayViewModelProtocol.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/OverlayViewModelProtocol.swift; sourceTree = "<group>"; };
		9BBFD847342BE87933D544A2FFD66F91 /* AvatarTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarTokenSet.swift; path = ios/FluentUI/Avatar/AvatarTokenSet.swift; sourceTree = "<group>"; };
		9C4CDD044B0B0B362242477461A1D887 /* PipelineContext.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineContext.swift; path = sdk/core/AzureCore/Source/Pipeline/PipelineContext.swift; sourceTree = "<group>"; };
		9CC109085972537E5F701565A5A2245A /* AzureCommunicationCommon.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCommunicationCommon.debug.xcconfig; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9DC04648E59A69FC8269BFA175E45DB9 /* MSFAvatarPresence.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MSFAvatarPresence.swift; path = ios/FluentUI/Avatar/MSFAvatarPresence.swift; sourceTree = "<group>"; };
		9DFBD9606077F980791A7E453C7B80C2 /* ViewExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ViewExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/ViewExtension.swift; sourceTree = "<group>"; };
		9E304F4FC271D0C3BB4BAF71B19BE56A /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ms.lproj/Localizable.strings; sourceTree = "<group>"; };
		9F095010ACD14AEE4E2B4CD0EF4379B4 /* IconButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconButton.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/IconButton.swift; sourceTree = "<group>"; };
		9F2B235E6DA4E7074956A11CE140F96C /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = hu.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		9F477F0DADAEEDC6A0C736D725A8FE47 /* FluentUI-apple.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = "FluentUI-apple.xcassets"; path = "apple/Resources/FluentUI-apple.xcassets"; sourceTree = "<group>"; };
		9FFC41A5A1944F5755936835A4FDEC1C /* ViewModifer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ViewModifer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/ViewModifer.swift; sourceTree = "<group>"; };
		A0137455B49A97371106A1571801083C /* PreviewAreaViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewAreaViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/PreviewAreaViewModel.swift; sourceTree = "<group>"; };
		A0916B7508E92C6994419DEA44DBD4EF /* LifeCycleReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LifeCycleReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/LifeCycleReducer.swift; sourceTree = "<group>"; };
		A0BA07FCACB4449236F9D2748E2B97C2 /* ParticipantCapability.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantCapability.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/ParticipantCapability.swift; sourceTree = "<group>"; };
		A0EF3CD0E5F5E543E10684302F560D5D /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		A10A8DF05447B6E1027958EF2A29CBB2 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "de-DE.lproj/Localizable.strings"; sourceTree = "<group>"; };
		A1A0C3BC83D750EC2B24F0A4B63CA13F /* SharingActivityView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharingActivityView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/SharingActivity/SharingActivityView.swift; sourceTree = "<group>"; };
		A1D949BFA428515E6E5BE51E2BC56AA4 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "ar-SA.lproj/Localizable.strings"; sourceTree = "<group>"; };
		A2DD50C462B2077D8009F309EC9A3DCA /* Copyable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Copyable.swift; path = sdk/core/AzureCore/Source/Util/Copyable.swift; sourceTree = "<group>"; };
		A31114F90D8DAD40C5677C1B8BC5DE4B /* MSFActivityIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MSFActivityIndicator.swift; path = ios/FluentUI/ActivityIndicator/MSFActivityIndicator.swift; sourceTree = "<group>"; };
		A3206EBFBBC3FB28B0166ACBBCCE1394 /* SourceViewSpace.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SourceViewSpace.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/FluentUI/Wrapper/SourceViewSpace.swift; sourceTree = "<group>"; };
		A3BD5FC2A4C8E478DD72642192A9EFEA /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = sk.lproj/Localizable.strings; sourceTree = "<group>"; };
		A46EF6701DCA54F35F4AFF6ADC7F4BF5 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = pl.lproj/Localizable.strings; sourceTree = "<group>"; };
		A4B660B5DAB93F47D1B002FF8327DFEF /* DrawerTransitionAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerTransitionAnimator.swift; path = ios/FluentUI/Drawer/DrawerTransitionAnimator.swift; sourceTree = "<group>"; };
		A5164CD72A2B9266F7DA4F055678C4FA /* AuthenticationPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AuthenticationPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/AuthenticationPolicy.swift; sourceTree = "<group>"; };
		A58FA4FEE4F8BC380EFA302A5425FBA6 /* ToastNotificationReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ToastNotificationReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/ToastNotificationReducer.swift; sourceTree = "<group>"; };
		A604BE74E03E15058F25D4F54BCE28C7 /* StyleProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StyleProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Style/StyleProvider.swift; sourceTree = "<group>"; };
		A6579F74A035C0F50EA1A33EBEFDD622 /* SwiftUI+ViewAnimation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "SwiftUI+ViewAnimation.swift"; path = "ios/FluentUI/Core/SwiftUI+ViewAnimation.swift"; sourceTree = "<group>"; };
		A6868E914AD372E6D5CC11202578F453 /* LocalizationOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalizationOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/LocalizationOptions.swift; sourceTree = "<group>"; };
		A6876A1C4E1E76EBF351C36543892A55 /* FontExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FontExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Utilities/FontExtension.swift; sourceTree = "<group>"; };
		A68E49E62ACD9C3D5DEEB9572C3E4974 /* ParticipantCapabilityType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantCapabilityType.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/ParticipantCapabilityType.swift; sourceTree = "<group>"; };
		A7B1D99A895872C72E7985A4A091A1CD /* AzureCommunicationCommon-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCommunicationCommon-prefix.pch"; sourceTree = "<group>"; };
		A8D08B31B1F8B137E16E26B0112CC47A /* Reducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Reducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/Reducer.swift; sourceTree = "<group>"; };
		A9103F6802C79CD9BF700B60D9F655D8 /* AzureCommunicationUICalling-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCommunicationUICalling-prefix.pch"; sourceTree = "<group>"; };
		AB133885A3F907D1DF292B6DBE0ED66E /* DrawerListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerListView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/DrawerListView.swift; sourceTree = "<group>"; };
		AB1A4903AA79B06795C4542AB7485FB1 /* NavigationState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NavigationState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/NavigationState.swift; sourceTree = "<group>"; };
		AB8C8B95709332F1A537618BAD51FB77 /* NavigationReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NavigationReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/NavigationReducer.swift; sourceTree = "<group>"; };
		AB8E7E3F0883631AB74A91703B7C662F /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = pl.lproj/Localizable.strings; sourceTree = "<group>"; };
		AC3E45754487C92C098E3F8482907685 /* DrawerPresentationController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerPresentationController.swift; path = ios/FluentUI/Drawer/DrawerPresentationController.swift; sourceTree = "<group>"; };
		ACD3A3EBB1C1EB3480DE9035D31868CD /* CallCompositeCallState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeCallState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeCallState.swift; sourceTree = "<group>"; };
		ACE425916CFC4C433A43D6426AA53C57 /* UIWindowExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UIWindowExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/UIWindowExtension.swift; sourceTree = "<group>"; };
		AD512F53FB63B4C2C4C108D1491B480C /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = zh.lproj/Localizable.strings; sourceTree = "<group>"; };
		AE3643C6FA971B88D53BEC7C54D9A909 /* ButtonViewDataAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonViewDataAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/ButtonViewDataAction.swift; sourceTree = "<group>"; };
		AE490866223E2BA6FC2DC3A2BC2D436E /* DrawerBodyTextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerBodyTextView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/DrawerBodyTextView.swift; sourceTree = "<group>"; };
		AF62620421CF1A8CC28D43CE04A10EFB /* CallKitOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallKitOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallKitOptions.swift; sourceTree = "<group>"; };
		B01E2E468B6311C25B86AF5CC1B5E291 /* Pods-iOSProjectTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-iOSProjectTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		B057843310E2BC4D1A11681D2B3D0C89 /* IncomingCallError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IncomingCallError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/IncomingCallError.swift; sourceTree = "<group>"; };
		B0F320A2591C6C10D912F67D2A54F374 /* BadgeLabel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BadgeLabel.swift; path = ios/FluentUI/Label/BadgeLabel.swift; sourceTree = "<group>"; };
		B1E28F5E472EE59C17A2F72ADB8B8219 /* MoreCallOptionsListViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MoreCallOptionsListViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/MoreCallOptions/MoreCallOptionsListViewModel.swift; sourceTree = "<group>"; };
		B22C9C500FE4BDFC523CD4534BFCC59A /* ButtonTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonTokenSet.swift; path = ios/FluentUI/Button/ButtonTokenSet.swift; sourceTree = "<group>"; };
		B2317AC05263AFC4A8B512518BBF793B /* TransportStage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TransportStage.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/TransportStage.swift; sourceTree = "<group>"; };
		B258201F04C519FB5DAE449AC0F41F17 /* SetupControlBarView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SetupControlBarView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Setup/SetupViewComponent/SetupControlBarView.swift; sourceTree = "<group>"; };
		B2631D831F5001B64C9BEDD6BCE13445 /* FluentTheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FluentTheme.swift; path = ios/FluentUI/Core/Theme/FluentTheme.swift; sourceTree = "<group>"; };
		B296179CD125E8BBFABA7FAFB11DFC9D /* CallScreenControlBarOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenControlBarOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallScreenControlBarOptions.swift; sourceTree = "<group>"; };
		B375851E21FB2D512DBBC63F1B045EC8 /* DeviceProviders.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DeviceProviders.swift; path = sdk/core/AzureCore/Source/Providers/DeviceProviders.swift; sourceTree = "<group>"; };
		B399BBBE8E4E00C64C8E28CF3BB81D68 /* CallCompositeOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeOptions.swift; sourceTree = "<group>"; };
		B3C7C3776A3DFC6AD9FCC27D5396DF44 /* BannerInfoType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BannerInfoType.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Banner/BannerInfoType.swift; sourceTree = "<group>"; };
		B4EB955FDA92A3A926D1071BF27D706C /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = nl.lproj/Localizable.strings; sourceTree = "<group>"; };
		B69EB277115DEA987339EBFBDBAF3B32 /* UIView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIView+Extensions.swift"; path = "ios/FluentUI/Extensions/UIView+Extensions.swift"; sourceTree = "<group>"; };
		B772B257C1BF436544C8E9075FE443AA /* AppState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/AppState.swift; sourceTree = "<group>"; };
		B77633AC8AEBFB5CF5825354030B78F9 /* ParticipantRoleEnum.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantRoleEnum.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/ParticipantRoleEnum.swift; sourceTree = "<group>"; };
		B7858A523D8CE7E9B32B3AD2E0FF1277 /* IconWithLabelButtonViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconWithLabelButtonViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/IconWithLabelButtonViewModel.swift; sourceTree = "<group>"; };
		B7BF04E1FB508C79CB3BF2DD42DFB902 /* SwipeActions-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SwipeActions-Info.plist"; sourceTree = "<group>"; };
		B8D4EBA729259D2F05338322DBF58230 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = de.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		B9D91EEAA0D57984BF35581E10E1148C /* ClientLogger.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ClientLogger.swift; path = sdk/core/AzureCore/Source/ClientLogger.swift; sourceTree = "<group>"; };
		B9EDA7F6A7BE98B46DD1FCF9FB1C5BDD /* CallingService.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingService.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/CallingService.swift; sourceTree = "<group>"; };
		BA072894EA7E403A1E8157D86EE6C9CE /* ButtonDynamicColors.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonDynamicColors.swift; path = ios/FluentUI/Core/Theme/Tokens/ButtonDynamicColors.swift; sourceTree = "<group>"; };
		BA5065ECA2F7D34771CAE78F77D368DD /* CaptionsErrorViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsErrorViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsErrorViewModel.swift; sourceTree = "<group>"; };
		BA610DA54C5958CA95C8A662D63B47CE /* TouchForwardingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TouchForwardingView.swift; path = ios/FluentUI/TouchForwardingView/TouchForwardingView.swift; sourceTree = "<group>"; };
		BC00BF7CD36D080BCC03D5345BDC8E1F /* LockPhoneOrientation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LockPhoneOrientation.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/LockPhoneOrientation.swift; sourceTree = "<group>"; };
		BC957AE6D4DB18DECF63614A413BA7A6 /* LocalUserState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalUserState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/LocalUserState.swift; sourceTree = "<group>"; };
		BCEE3114197F3B1431067D326CC8A4C3 /* Action.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Action.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/Action.swift; sourceTree = "<group>"; };
		BD3235C5A9DF6ABC738F712772FC895C /* TelemetryOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TelemetryOptions.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/TelemetryOptions.swift; sourceTree = "<group>"; };
		BEC46B7DA2536B8F8F3B00D521A8E3E6 /* ThreadSafeRefreshableAccessTokenCache.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ThreadSafeRefreshableAccessTokenCache.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/ThreadSafeRefreshableAccessTokenCache.swift; sourceTree = "<group>"; };
		BEE202518DC955F43703770B45A5927A /* HeadersValidationPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HeadersValidationPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/HeadersValidationPolicy.swift; sourceTree = "<group>"; };
		BEF4C977599D76381B061565A7F68DD1 /* URLHTTPResponse.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URLHTTPResponse.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/URLHTTPResponse.swift; sourceTree = "<group>"; };
		BF8CA1902A3E746395C999BCE8E1AB5A /* ContainerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ContainerView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Container/ContainerView.swift; sourceTree = "<group>"; };
		BFA59160085822A230D4D3F2CAC27407 /* CancellationToken.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CancellationToken.swift; path = sdk/core/AzureCore/Source/DataStructures/CancellationToken.swift; sourceTree = "<group>"; };
		BFFA8F5146A63328959236141F803FCA /* ParticipantGridCellView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridCellView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/Cell/ParticipantGridCellView.swift; sourceTree = "<group>"; };
		C0712082E8DD32619C6C19BF72BB19C8 /* SharingActivityContainerController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharingActivityContainerController.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/SharingActivity/SharingActivityContainerController.swift; sourceTree = "<group>"; };
		C084400BE0A523B80129C509BBD7C214 /* SwipeActions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwipeActions.swift; path = Sources/SwipeActions/SwipeActions.swift; sourceTree = "<group>"; };
		C0A262F8DBAB0D0ADA5D35447063BA98 /* PopupMenuSection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PopupMenuSection.swift; path = "ios/FluentUI/Popup Menu/PopupMenuSection.swift"; sourceTree = "<group>"; };
		C0CF641A4F8C6F3D58B18E9851F765A8 /* HeadersPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HeadersPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/HeadersPolicy.swift; sourceTree = "<group>"; };
		C1B3E2B0B90D1A7BFD51C6D889DBCF74 /* CaptionsLanguageListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsLanguageListView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsLanguageListView.swift; sourceTree = "<group>"; };
		C211D9644098BE447DD459B8CD561AC4 /* Pods-iOSProject.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProject.release.xcconfig"; sourceTree = "<group>"; };
		C2B29DCE4D2A947D29A24C56E0D75F90 /* PipManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/PipManager.swift; sourceTree = "<group>"; };
		C36108D33AF689B99BBE37A24F9A438B /* AccessibilityProviderProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AccessibilityProviderProtocol.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/AccessibilityProviderProtocol.swift; sourceTree = "<group>"; };
		C399A358BC2C490D90AC384CADCDCD7E /* Pods-iOSProjectTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-iOSProjectTests-umbrella.h"; sourceTree = "<group>"; };
		C54F33F7270ED5AE6F7A5B1BD8AE7878 /* Colors.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Colors.swift; path = ios/FluentUI/Core/Colors.swift; sourceTree = "<group>"; };
		C55567F7C85C976E5A389C0BFF8D6830 /* SupportedLocale.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SupportedLocale.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/SupportedLocale.swift; sourceTree = "<group>"; };
		C5D00D63466046D0CC8CEC2C684AF872 /* AudioSessionManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioSessionManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/AudioSessionManager.swift; sourceTree = "<group>"; };
		C7A1EF53D3DD231F158CD09CAE20A135 /* ApplicationUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ApplicationUtil.swift; path = sdk/core/AzureCore/Source/Util/ApplicationUtil.swift; sourceTree = "<group>"; };
		C92E0C6D24CD0990BDFDB37080FFC7AC /* LocalOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalOptions.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/LocalOptions.swift; sourceTree = "<group>"; };
		C97B85DEC7A69CC0F7604C31FC0FFF69 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = he.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		C9C0E58C836CEECECBBA614EEAE53DC2 /* AzureCommunicationUICalling */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AzureCommunicationUICalling; path = AzureCommunicationUICalling.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C9D33859F40D0B95059452BDCF71D7B4 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hant-TW.lproj/Localizable.strings"; sourceTree = "<group>"; };
		C9D34FEC8866D7B0FFBB39A126689A95 /* SwipeActions-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SwipeActions-prefix.pch"; sourceTree = "<group>"; };
		CAFC82777C625A58E3DE6A302530E06B /* BottomDrawer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BottomDrawer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/BottomDrawer.swift; sourceTree = "<group>"; };
		CB511B1F8C4FA9EA40D159DC863E075E /* LobbyOverlayView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyOverlayView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Overlay/LobbyOverlayView.swift; sourceTree = "<group>"; };
		CBA0AFA18700F0D16059DFC2D214E346 /* KeybaordResponder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = KeybaordResponder.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/KeybaordResponder.swift; sourceTree = "<group>"; };
		CBC8D9E4D8904DC615DD604E62F32ADA /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "nb-NO.lproj/Localizable.strings"; sourceTree = "<group>"; };
		CBD260F08947A8AC8689CC28B309D1A6 /* Separator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Separator.swift; path = ios/FluentUI/Separator/Separator.swift; sourceTree = "<group>"; };
		CC065F129F325F4ADBD1ED5F7CAD8468 /* ACSFeatures.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSFeatures.h; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSFeatures.h"; sourceTree = "<group>"; };
		CC75BD097424C53906FABBAE47E3893A /* NavigationRouter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NavigationRouter.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Navigation/NavigationRouter.swift; sourceTree = "<group>"; };
		CC83D9FAB90DB69EF02A99982229F355 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		CC8622DA90237565DB4F6D542F8B2352 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = fi.lproj/Localizable.strings; sourceTree = "<group>"; };
		CCBB2A8C7D82827C636E1ED1C11C21D2 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		CD385314B765653786FE3902103782F8 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		CD7CCB5B049008FD2F13E68BF4665117 /* CaptionsRttListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttListView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsRttListView.swift; sourceTree = "<group>"; };
		CE62285A8986240D418B928205FB0F8B /* LobbyWaitingHeaderViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LobbyWaitingHeaderViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/LobbyWaitingHeaderViewModel.swift; sourceTree = "<group>"; };
		CE65509D035ACE1E4A3C088E261B1328 /* CALayer+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CALayer+Extensions.swift"; path = "ios/FluentUI/Drawer/CALayer+Extensions.swift"; sourceTree = "<group>"; };
		CEF302F2865D5EF55FFCF3E9F55120F7 /* ErrorInfoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ErrorInfoView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Error/ErrorInfoView.swift; sourceTree = "<group>"; };
		CF49763E8F89C09177863A3ABAAA9958 /* NotificationCenterName.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NotificationCenterName.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/NotificationCenterName.swift; sourceTree = "<group>"; };
		CF7873184DCF81262BF51506E548042F /* Pods-iOSProject-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-iOSProject-frameworks.sh"; sourceTree = "<group>"; };
		CF7E40D9F1651957F94C8F63B2441FFA /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = es.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		CF9561D60C46432EA43238A09800C510 /* AudioSessionReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioSessionReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/AudioSessionReducer.swift; sourceTree = "<group>"; };
		CFA4F9FB35E3FF33F252B3E6E5F869E0 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "ko-KR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		CFB59273F188B58A956814DA4FDBE61B /* AudioDeviceListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioDeviceListView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/AudioSelection/AudioDeviceListView.swift; sourceTree = "<group>"; };
		CFED14DAFEAC58EF0FF13D249279019C /* CaptionsRttInfoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttInfoViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsRttInfoViewModel.swift; sourceTree = "<group>"; };
		D000FFF6214A0D59E84174F78D66A4A5 /* DefaultUserState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DefaultUserState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/DefaultUserState.swift; sourceTree = "<group>"; };
		D032AFA98F2B30BDC8E96F4147BD4B62 /* CommunicationTokenCredentialProviding.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommunicationTokenCredentialProviding.swift; path = sdk/communication/AzureCommunicationCommon/Source/Authentication/CommunicationTokenCredentialProviding.swift; sourceTree = "<group>"; };
		D1529838C2753DB8D0A725CB14A34C0D /* MessageBarDiagnosticViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MessageBarDiagnosticViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Diagnostics/MessageBarDiagnosticViewModel.swift; sourceTree = "<group>"; };
		D161AA6B9F66CB78E041E11E46771F3D /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = nl.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		D17750201604D3C0371C403D9623B9B3 /* UpdatableOptionsManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UpdatableOptionsManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/UpdatableOptionsManager.swift; sourceTree = "<group>"; };
		D1AF8F21737D67ACC40F01E672C0416B /* SwipeActions-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SwipeActions-dummy.m"; sourceTree = "<group>"; };
		D1EE3C1452BD5D657E2171DF60B8113D /* TableViewCellTokenSet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableViewCellTokenSet.swift; path = "ios/FluentUI/Table View/TableViewCellTokenSet.swift"; sourceTree = "<group>"; };
		D1F9A566DC704B783820D0F2B94634A7 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "zh-Hans-CN.lproj/Localizable.strings"; sourceTree = "<group>"; };
		D2EFF46B88A977C51D8D2AE18F9D8678 /* AzureCommunicationCalling-Swift.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AzureCommunicationCalling-Swift.h"; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h"; sourceTree = "<group>"; };
		D424C6095677E67FDC7EC5033AC9B352 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = hr.lproj/Localizable.strings; sourceTree = "<group>"; };
		D4C17EAC97A56C33D1DCBA96F9022867 /* RetryPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RetryPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/RetryPolicy.swift; sourceTree = "<group>"; };
		D4CA5FAC044097ED5E5D38C6647E4EFF /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		D4D2597860D550CA0FA0D7917A92EF5D /* CapabilityResolutionReason.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapabilityResolutionReason.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Model/CapabilityResolutionReason.swift; sourceTree = "<group>"; };
		D51B69EE13AFC7B1105AED2DE0003747 /* ACSDiagnosticQualityExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ACSDiagnosticQualityExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/ACSDiagnosticQualityExtension.swift; sourceTree = "<group>"; };
		D55565D4DD5BBC5ECD09D15C34DD344B /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = cs.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		D61B29536A94347135E1D3D9762271EF /* ACSCallingStateExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ACSCallingStateExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Service/Calling/AzureCommunicationCalling/ACSCallingStateExtension.swift; sourceTree = "<group>"; };
		D6481E98B7A38F2B8A5A633E8F041615 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6E5D1979C95206398F3E37F61401690 /* ButtonState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Utilities/ButtonState.swift; sourceTree = "<group>"; };
		D6E8CE915C73A74556C09B201EADECC9 /* TransportOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TransportOptions.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/TransportOptions.swift; sourceTree = "<group>"; };
		D711CA6E12B3027CF883DACF065B98C2 /* Collections.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Collections.swift; path = sdk/core/AzureCore/Source/DataStructures/Collections.swift; sourceTree = "<group>"; };
		D717ADE98D1F65B48398DE5C0111F92F /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "it-IT.lproj/Localizable.strings"; sourceTree = "<group>"; };
		D9347FFA384836ED2CFC477F03046E68 /* CaptionsRttInfoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptionsRttInfoView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/CaptionsRtt/CaptionsRttInfoView.swift; sourceTree = "<group>"; };
		D9B6E733557E9DB456936CBA5C401B96 /* ContentHeightResolutionContext.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ContentHeightResolutionContext.swift; path = "ios/FluentUI/Bottom Sheet/ContentHeightResolutionContext.swift"; sourceTree = "<group>"; };
		DA553BDCA19BCAC7527E3CA1BAD9D519 /* SupportFormView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SupportFormView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/SupportForm/SupportFormView.swift; sourceTree = "<group>"; };
		DA78D346CFEABC00DADEB7E40F34EAFF /* CallScreenInfoHeaderState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallScreenInfoHeaderState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/CallScreenInfoHeaderState.swift; sourceTree = "<group>"; };
		DB57C5C0EC0087608DB222E64B01247B /* CallCompositeUserReportedError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeUserReportedError.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeUserReportedError.swift; sourceTree = "<group>"; };
		DB744CF8EDA2762E0B6DB6E8F39926D7 /* ParticipantsListCellViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantsListCellViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ParticipantsList/ParticipantsListCellViewModel.swift; sourceTree = "<group>"; };
		DB9FDF128C332399D1C6B509AFA9ABD2 /* Pods-iOSProject-iOSProjectUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSProject-iOSProjectUITests.debug.xcconfig"; sourceTree = "<group>"; };
		DBFB4FCCACDFCE7FCF5EB367D3A30495 /* Fonts.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Fonts.swift; path = ios/FluentUI/Core/Fonts.swift; sourceTree = "<group>"; };
		DC8B7950F36091E881D0C0EB108A56D7 /* Pods-iOSProject */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-iOSProject"; path = Pods_iOSProject.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DC96DA053E2A69F84FE5C41A5EAC1470 /* AudioDeviceType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioDeviceType.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Manager/AudioDeviceType.swift; sourceTree = "<group>"; };
		DCE7AE9F622C44DC0FD8C2FA90CCBD8F /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ko.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		DCF47F8FA0E92D20B30CBD4EE048ACD5 /* DynamicColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DynamicColor.swift; path = ios/FluentUI/Core/Theme/Tokens/DynamicColor.swift; sourceTree = "<group>"; };
		DCFAF388EA4F1ECC05D192D188DFEE93 /* AvatarGroupModifiers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AvatarGroupModifiers.swift; path = ios/FluentUI/AvatarGroup/AvatarGroupModifiers.swift; sourceTree = "<group>"; };
		DDA6CB85C0A67195A3930E0AA77D8E85 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "zh-Hant.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		DDBE1C2D993E59E9BBFFF6534CA38BF8 /* AzureCommunicationUICalling.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AzureCommunicationUICalling.modulemap; sourceTree = "<group>"; };
		DE765ED433C81E1C1195A5D48BAF816D /* ResourceBundle-FluentUIResources-ios-MicrosoftFluentUI-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-FluentUIResources-ios-MicrosoftFluentUI-Info.plist"; sourceTree = "<group>"; };
		DE7DC946E1FDE62288D210CA764AA945 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		DEA207A747DFC202BB7FAF307570B629 /* AzureCommunicationCommon-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AzureCommunicationCommon-dummy.m"; sourceTree = "<group>"; };
		DF3BF5CBC4BB9CA8283CC316BB8065E9 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ro.lproj/Localizable.strings; sourceTree = "<group>"; };
		DF4EE540C6CDF62F03E8B54F4860C8B7 /* TableViewHeaderFooterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableViewHeaderFooterView.swift; path = "ios/FluentUI/Table View/TableViewHeaderFooterView.swift"; sourceTree = "<group>"; };
		DFAB7AA29AB873290F2BB6A0CBA8E41C /* UserAgentPolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UserAgentPolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/UserAgentPolicy.swift; sourceTree = "<group>"; };
		DFEA35640AC140925F2160D468256639 /* ParticipantGridView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantGridView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/ParticipantGridView.swift; sourceTree = "<group>"; };
		E0EB44C551D89663740A9BFB89BACDFA /* AudioDevicesListViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioDevicesListViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/AudioSelection/AudioDevicesListViewModel.swift; sourceTree = "<group>"; };
		E0ED952467261B17919AFE46AB64006F /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = pt.lproj/Localizable.strings; sourceTree = "<group>"; };
		E1514DA8A0B7A198B45367C7F44BF6D6 /* Button.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Button.swift; path = ios/FluentUI/Button/Button.swift; sourceTree = "<group>"; };
		E1E994C8CDAD72BE943ECE7BB6D2DDA8 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = tr.lproj/Localizable.strings; sourceTree = "<group>"; };
		E1F4895CC34CF03EFD122E0BBBF3421B /* ParticipantMenuViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantMenuViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ParticipantActions/ParticipantMenuViewModel.swift; sourceTree = "<group>"; };
		E27C775AE1409D946C84E0E5A6090D9B /* CallKitRemoteInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallKitRemoteInfo.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallKitRemoteInfo.swift; sourceTree = "<group>"; };
		E2C73009B2782FA4E19C89ECE8DE42BA /* InfoHeaderViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = InfoHeaderViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/InfoHeaderViewModel.swift; sourceTree = "<group>"; };
		E2E998DD66E9652A98A669608F1AE986 /* ColorExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ColorExtension.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/ColorExtension.swift; sourceTree = "<group>"; };
		E3B3E192E42DA21880DC62EA215DB7B3 /* LinearGradientInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LinearGradientInfo.swift; path = ios/FluentUI/Core/Theme/Tokens/LinearGradientInfo.swift; sourceTree = "<group>"; };
		E3BDDBF7F0690434DE2F2B59F3399C49 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = id.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		E404C34A082E5C697545707969AEFBE8 /* XMLModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = XMLModel.swift; path = sdk/core/AzureCore/Source/DataStructures/XMLModel.swift; sourceTree = "<group>"; };
		E494AFE4F4CD3C7CA87384C7235B0ED9 /* CallingMiddlewareHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallingMiddlewareHandler.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Middleware/CallingMiddlewareHandler.swift; sourceTree = "<group>"; };
		E553BAEE1695E7E48C3A17EB5218302E /* AzureCommunicationUICalling-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AzureCommunicationUICalling-Info.plist"; sourceTree = "<group>"; };
		E5A60CAF27A6D6477DC41D42EF66FEC2 /* ErrorReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ErrorReducer.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Reducer/ErrorReducer.swift; sourceTree = "<group>"; };
		E6BD320AE43B783EDEBFBFD37F071226 /* ActivityIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ActivityIndicator.swift; path = ios/FluentUI/ActivityIndicator/ActivityIndicator.swift; sourceTree = "<group>"; };
		E747587576023D9CE752536BA706103B /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "zh-Hans.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		E7F42A5449586A5CDF7FFCC2613B8386 /* CompositeViewFactory.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeViewFactory.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Factories/CompositeViewFactory.swift; sourceTree = "<group>"; };
		E91C7357AB187817E0734576154542B8 /* AzureCommunicationCalling-Swift.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "AzureCommunicationCalling-Swift.h"; path = "AzureCommunicationCalling.xcframework/ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h"; sourceTree = "<group>"; };
		E9F4C23561463D8B5A6E56840F88BEAD /* PipelineResponse.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PipelineResponse.swift; path = sdk/core/AzureCore/Source/Pipeline/PipelineResponse.swift; sourceTree = "<group>"; };
		E9FD78EC0FD1B3A8E86D82BFE118A7C3 /* AzureCore.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AzureCore.debug.xcconfig; sourceTree = "<group>"; };
		EA418E2843D9E62F06D607FC0724031C /* RemoteParticipantsState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoteParticipantsState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/RemoteParticipantsState.swift; sourceTree = "<group>"; };
		EA9D5783068A0AA7BFF369C10160564C /* ZoomableVideoRenderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ZoomableVideoRenderView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/Grid/Cell/ZoomableVideoRenderView.swift; sourceTree = "<group>"; };
		EABA0734D2A5CC5FB990DD568C7D5F1A /* ACSStreamSize.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ACSStreamSize.h; path = "AzureCommunicationCalling.xcframework/ios-arm64/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h"; sourceTree = "<group>"; };
		EADD14CE206148D922688699F4B0C66E /* LocalizationKey.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalizationKey.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Utilities/LocalizationKey.swift; sourceTree = "<group>"; };
		EBADC2E0AE01DD4A333262E4D64F8858 /* Pods-iOSProject-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-iOSProject-acknowledgements.markdown"; sourceTree = "<group>"; };
		EC2E73C06448CD60737C47EE44751459 /* GlobalTokens.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GlobalTokens.swift; path = ios/FluentUI/Core/Theme/Tokens/GlobalTokens.swift; sourceTree = "<group>"; };
		EC4A373D3270D2DEF3C8658460600A61 /* CompositeButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CompositeButton.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/FluentUI/Wrapper/CompositeButton.swift; sourceTree = "<group>"; };
		ED27BEB9573F9494E493ED01EF3332E6 /* BottomToastView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BottomToastView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ToastNotification/BottomToastView.swift; sourceTree = "<group>"; };
		EDD93D840930CB9A58E7C2948A203B73 /* PrimaryButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PrimaryButton.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Button/PrimaryButton.swift; sourceTree = "<group>"; };
		EDE99E3552F3017D5E5DBE987A399E75 /* Pods-iOSProject.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-iOSProject.modulemap"; sourceTree = "<group>"; };
		EE826D52AF3C62D88E8915C5414A6D0C /* ActivityIndicatorModifiers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ActivityIndicatorModifiers.swift; path = ios/FluentUI/ActivityIndicator/ActivityIndicatorModifiers.swift; sourceTree = "<group>"; };
		EEB43FF77D3B83CB073D26F18012DA90 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "nb-NO.lproj/Localizable.strings"; sourceTree = "<group>"; };
		F014B56A21A122D051BA050708238F7A /* UIKit+SwiftUI_interoperability.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIKit+SwiftUI_interoperability.swift"; path = "ios/FluentUI/Core/UIKit+SwiftUI_interoperability.swift"; sourceTree = "<group>"; };
		F01A889FBAD31D7921A763022233468E /* LocaleInfoProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocaleInfoProvider.swift; path = sdk/core/AzureCore/Source/Providers/LocaleInfoProvider.swift; sourceTree = "<group>"; };
		F026B274B4D4612459F68A558F8B9232 /* AzureCommunicationCommon-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCommunicationCommon-umbrella.h"; sourceTree = "<group>"; };
		F089D9BFC3EB292E450C8632A4D6D979 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = da.lproj/Localizable.strings; sourceTree = "<group>"; };
		F104BE6C6A9888A94273C854EEDB9FE9 /* Obscurable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Obscurable.swift; path = ios/FluentUI/Obscurable/Obscurable.swift; sourceTree = "<group>"; };
		F1FAB415AA27006FA2C4D6060C96D007 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = sk.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		F2023AB9DE89186B164BB61E5FF6F424 /* BlurringView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BlurringView.swift; path = ios/FluentUI/Obscurable/BlurringView.swift; sourceTree = "<group>"; };
		F2559CE8EBDC6304277729C5FD5B62B5 /* DrawerShadowView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerShadowView.swift; path = ios/FluentUI/Drawer/DrawerShadowView.swift; sourceTree = "<group>"; };
		F281EDBAD41DA89812A4F0E3F6B08F28 /* SwipeActions-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SwipeActions-umbrella.h"; sourceTree = "<group>"; };
		F2B2AA8A262B395B05D77D9EF2BCD17B /* ResizingHandleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ResizingHandleView.swift; path = ios/FluentUI/ResizingHandleView/ResizingHandleView.swift; sourceTree = "<group>"; };
		F311DCBB6CE9BF641EB4C151B7B1789A /* CallPipVideoViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallPipVideoViewController.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/Pip/CallPipVideoViewController.swift; sourceTree = "<group>"; };
		F35BB53E051EDDF790EAA717C5AD3D7C /* BannerTextViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BannerTextViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Calling/CallingViewComponent/Banner/BannerTextViewModel.swift; sourceTree = "<group>"; };
		F3A7FB7B5BB557CFC57AC42DC63057DB /* BaseLocalizationProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BaseLocalizationProvider.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/BaseLocalizationProvider.swift; sourceTree = "<group>"; };
		F3ACAA6E7964BD6B4971927811F1BA4B /* OrientationManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OrientationManager.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICommon/Sources/AzureCommunicationUICommon/OrientationManager.swift; sourceTree = "<group>"; };
		F48461459D817F1768F10E0326ABDAB7 /* ContentDecodePolicy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ContentDecodePolicy.swift; path = sdk/core/AzureCore/Source/Pipeline/Policies/ContentDecodePolicy.swift; sourceTree = "<group>"; };
		F48FA0C2B4AAA1C19F1278D784FEC1C0 /* SwipeActions.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SwipeActions.debug.xcconfig; sourceTree = "<group>"; };
		F617E050123E74BFEF251A9548E361C8 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = fr.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		F634937F33B5FD66068E7702E56736E7 /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = "en-GB.lproj/Localizable.strings"; sourceTree = "<group>"; };
		F65DFAB111B4D66F81FD48CF18BEEFDA /* CallHistoryRepository.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallHistoryRepository.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Data/CallHistoryRepository.swift; sourceTree = "<group>"; };
		F664E0FD672EC99E6B911E2754BDBCD6 /* DrawerTitleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DrawerTitleView.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/Drawer/Views/DrawerTitleView.swift; sourceTree = "<group>"; };
		F66AA86C0D9004E91FE8FA2AAB29BED1 /* MicrosoftFluentUI-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MicrosoftFluentUI-dummy.m"; sourceTree = "<group>"; };
		F6F4C386E58F309333EF74C5DD9450D0 /* VisibilityState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VisibilityState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/VisibilityState.swift; sourceTree = "<group>"; };
		F7324E9A62AE54E6C041797FE58F8B2C /* Localizable.strings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.strings; name = Localizable.strings; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		F77927DBD38E462175E5B4220BC45850 /* DataStringConvertible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DataStringConvertible.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/DataStringConvertible.swift; sourceTree = "<group>"; };
		F7C6D6EB5615EE642CB6AD7F12949928 /* HTTPRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HTTPRequest.swift; path = sdk/core/AzureCore/Source/Pipeline/Transport/HTTPRequest.swift; sourceTree = "<group>"; };
		F7D2845663B59084595D059B1ABDEC40 /* MicrosoftFluentUI-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MicrosoftFluentUI-Info.plist"; sourceTree = "<group>"; };
		F80174F9963D4E5D8DFCB82032F35648 /* ButtonViewDataState.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ButtonViewDataState.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/State/ButtonViewDataState.swift; sourceTree = "<group>"; };
		F9E7C2367480F88523603EDCB00C7F35 /* CallCompositeDismissed.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CallCompositeDismissed.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/CallCompositeOptions/CallCompositeDismissed.swift; sourceTree = "<group>"; };
		FA20A5B2176EB4E24B773E4C3B84D377 /* Pods-iOSProject-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-iOSProject-dummy.m"; sourceTree = "<group>"; };
		FA8D716813D3130FA8106B1CBC836973 /* ParticipantsListViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ParticipantsListViewModel.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/ViewComponents/ParticipantsList/ParticipantsListViewModel.swift; sourceTree = "<group>"; };
		FB3BA383AFAC1F032391FEBC4CFC0EE5 /* AzureCore-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AzureCore-umbrella.h"; sourceTree = "<group>"; };
		FB9E810146915DE74F09C23CFF866458 /* XMLMap.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = XMLMap.swift; path = sdk/core/AzureCore/Source/DataStructures/XMLMap.swift; sourceTree = "<group>"; };
		FBD6D72FCC3C0A4D97D30AA94B92A02F /* IconAndLabelConversion.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IconAndLabelConversion.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Presentation/SwiftUI/Utilities/IconAndLabelConversion.swift; sourceTree = "<group>"; };
		FC3C6B644945B153ED861D09D9C6ECE7 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = "en-GB.lproj/Localizable.stringsdict"; sourceTree = "<group>"; };
		FD76B04491BD820A0DA1E2B43CEDC6E2 /* AliasTokens.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AliasTokens.swift; path = ios/FluentUI/Core/Theme/Tokens/AliasTokens.swift; sourceTree = "<group>"; };
		FDDFF5CD8018A225E1C89D80901744D5 /* RttAction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RttAction.swift; path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Redux/Action/RttAction.swift; sourceTree = "<group>"; };
		FE44E69AEBE1F09BC91574F644148267 /* Localizable.stringsdict */ = {isa = PBXFileReference; includeInIndex = 1; name = Localizable.stringsdict; path = ar.lproj/Localizable.stringsdict; sourceTree = "<group>"; };
		FE4CEA862D8B884C3F02E92096642407 /* MSFAvatarGroup.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MSFAvatarGroup.swift; path = ios/FluentUI/AvatarGroup/MSFAvatarGroup.swift; sourceTree = "<group>"; };
		FE6AEDF4510C9D581D5977A9E57F0A08 /* Pods-iOSProjectTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-iOSProjectTests.modulemap"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0BBA6388F8B6DC1044DAE9138E4ADB93 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8BEEA12FFBF996445E1CB1DC19BF86BB /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BB057DA4ED8AE11CB9DE32E56BA8339 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				36F09F2364AC6A5C0596F9DAD00AE9FB /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59F2608627736051701E2F97552BFBC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0FB150AEBDD3215124F8049671FA51C5 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F035379608155BAE1CA970519960403 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6F8013D6932F3E3440BBB40E273A4094 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E19800BEEE6A38F84E5BC0893EA4A1A9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7606FBAB240087AEA19C8114CD4671F1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F7934210185E2437204B29F85C6AE74D /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A30EE9324981951053529CAB38F445C1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				B04C0979DCAA442690A53FC9D6CD24D3 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD795327359E6EDC648AB3D8D6542CC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FFCC08698A631203ACD36044D3811B47 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D0A4ADAE8A03B21E43AD18C718DDBF70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8B3E77B55CD3B6D4C66178F193A88A4F /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0EDB929F00F24DE9042FB6C788AE30C1 /* Label_ios */ = {
			isa = PBXGroup;
			children = (
				B0F320A2591C6C10D912F67D2A54F374 /* BadgeLabel.swift */,
				5C2ECC3C94603626E6A959FC467460AD /* Label.swift */,
			);
			name = Label_ios;
			sourceTree = "<group>";
		};
		10DD5AC62DB19BED2CA385217C290B25 /* Resources */ = {
			isa = PBXGroup;
			children = (
				9F477F0DADAEEDC6A0C736D725A8FE47 /* FluentUI-apple.xcassets */,
				488F28FA0B8F2A146DDB19B952EE5EF3 /* FluentUI-ios.xcassets */,
				3460057AB7FC55D43DAD719B427A91F6 /* Localizable.strings */,
				7708492A1C3815979A7398F14E2015BD /* Localizable.stringsdict */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		1149626FB9F98B855D7758629504A397 /* AvatarGroup_ios */ = {
			isa = PBXGroup;
			children = (
				9888C41B7DD150EC19D9EEFF14FC5631 /* AvatarGroup.swift */,
				DCFAF388EA4F1ECC05D192D188DFEE93 /* AvatarGroupModifiers.swift */,
				68E593E829379139F55496F13110A628 /* AvatarGroupTokenSet.swift */,
				FE4CEA862D8B884C3F02E92096642407 /* MSFAvatarGroup.swift */,
			);
			name = AvatarGroup_ios;
			sourceTree = "<group>";
		};
		23F94FB6CD10DE8799178EE5E36A5A44 /* AzureCore */ = {
			isa = PBXGroup;
			children = (
				91A315E48F79E42026749857DF9D40FB /* AddDatePolicy.swift */,
				C7A1EF53D3DD231F158CD09CAE20A135 /* ApplicationUtil.swift */,
				A5164CD72A2B9266F7DA4F055678C4FA /* AuthenticationPolicy.swift */,
				3C31F64E6A2412601E3768516A40C307 /* AzureCodable.swift */,
				636F88A1B58EFDD74E5FBDD6AA7867F2 /* AzureDate.swift */,
				5437291470A18CFC7EE83A02866CFAED /* AzureTask.swift */,
				72277D2220FD0CC7547C22183F0C6EDF /* BundleInfoProvider.swift */,
				BFA59160085822A230D4D3F2CAC27407 /* CancellationToken.swift */,
				B9D91EEAA0D57984BF35581E10E1148C /* ClientLogger.swift */,
				D711CA6E12B3027CF883DACF065B98C2 /* Collections.swift */,
				F48461459D817F1768F10E0326ABDAB7 /* ContentDecodePolicy.swift */,
				62755A819E1320B8A63CEA61C0EC9153 /* ConvertingInitializers.swift */,
				A2DD50C462B2077D8009F309EC9A3DCA /* Copyable.swift */,
				0CDA2191AF0E7C82F780C282C816224D /* CryptoUtil.swift */,
				F77927DBD38E462175E5B4220BC45850 /* DataStringConvertible.swift */,
				B375851E21FB2D512DBBC63F1B045EC8 /* DeviceProviders.swift */,
				9B8E9241C0858619F232FA308E9B4BDC /* Errors.swift */,
				C0CF641A4F8C6F3D58B18E9851F765A8 /* HeadersPolicy.swift */,
				BEE202518DC955F43703770B45A5927A /* HeadersValidationPolicy.swift */,
				878629BA144808F20AA70C561DA96B78 /* HTTPHeader.swift */,
				2C462A53B5181C8E6CC6636F25F9E2D2 /* HTTPMethod.swift */,
				F7C6D6EB5615EE642CB6AD7F12949928 /* HTTPRequest.swift */,
				79C7C977DC9B706F740B9806B6DA88FD /* HTTPResponse.swift */,
				1B90B78A9861159EC7B6BD9BD0B62B75 /* KeychainUtil.swift */,
				F01A889FBAD31D7921A763022233468E /* LocaleInfoProvider.swift */,
				18A4F477806D5E5E2B30F37AD4BE1A49 /* LoggingPolicy.swift */,
				9404205354FF9A7223C96D0934A51ED7 /* NormalizeETagPolicy.swift */,
				0738CDC794B122B909F2CB77D9FF30E4 /* Pipeline.swift */,
				65BF1421F6F1F18A74621DBF779AAA44 /* PipelineClient.swift */,
				9C4CDD044B0B0B362242477461A1D887 /* PipelineContext.swift */,
				5A962D257B2DCC49DBA3298654A61BD3 /* PipelineRequest.swift */,
				E9F4C23561463D8B5A6E56840F88BEAD /* PipelineResponse.swift */,
				941AFE87345A1A2B2FE73F2F5141B784 /* PipelineStage.swift */,
				******************************** /* PlatformInfoProvider.swift */,
				011E504D718112F73B239A9B72981DAD /* ReachabilityManager.swift */,
				801D77A0AAC9D93241C56A9E090A21CA /* ReachabilityManagerType.swift */,
				1997DC2FA21B7EFA5ACE0B182B731450 /* RegexUtil.swift */,
				4423E83C671B759C5512539D57237C05 /* RequestIdPolicy.swift */,
				1CC9DB042BB7452C8DF4780D181AF9EF /* RequestParameters.swift */,
				9B8A85DC1E10B66D2B686119035414AE /* RequestString.swift */,
				D4C17EAC97A56C33D1DCBA96F9022867 /* RetryPolicy.swift */,
				2FE143DAF7581E7C92674E75F7017A6A /* StringUtil.swift */,
				BD3235C5A9DF6ABC738F712772FC895C /* TelemetryOptions.swift */,
				D6E8CE915C73A74556C09B201EADECC9 /* TransportOptions.swift */,
				B2317AC05263AFC4A8B512518BBF793B /* TransportStage.swift */,
				BEF4C977599D76381B061565A7F68DD1 /* URLHTTPResponse.swift */,
				29D59772F3D34B20F3D5E13E0817B254 /* URLSessionTransport.swift */,
				280D7784580701D3A46D65D9E36C500B /* URLUtil.swift */,
				DFAB7AA29AB873290F2BB6A0CBA8E41C /* UserAgentPolicy.swift */,
				FB9E810146915DE74F09C23CFF866458 /* XMLMap.swift */,
				E404C34A082E5C697545707969AEFBE8 /* XMLModel.swift */,
				87EC8CD373B0D2683C8B40EFAFDBE3B1 /* XMLTree.swift */,
				AB12D332A5F3B4967CBB3FA36466ADB6 /* Support Files */,
			);
			name = AzureCore;
			path = AzureCore;
			sourceTree = "<group>";
		};
		2F077EB93A5559349C2844F65BE3D393 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				75FA895C9FED615162E47E559ED70D68 /* AzureCommunicationCommon.modulemap */,
				DEA207A747DFC202BB7FAF307570B629 /* AzureCommunicationCommon-dummy.m */,
				2EE96CD2D9B1531C227835032BA56783 /* AzureCommunicationCommon-Info.plist */,
				A7B1D99A895872C72E7985A4A091A1CD /* AzureCommunicationCommon-prefix.pch */,
				F026B274B4D4612459F68A558F8B9232 /* AzureCommunicationCommon-umbrella.h */,
				9CC109085972537E5F701565A5A2245A /* AzureCommunicationCommon.debug.xcconfig */,
				5FD46B21C237D0FE9D8F321DB739267A /* AzureCommunicationCommon.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AzureCommunicationCommon";
			sourceTree = "<group>";
		};
		3D53EF32748B442F858BE62476A2CF00 /* Separator_ios */ = {
			isa = PBXGroup;
			children = (
				CBD260F08947A8AC8689CC28B309D1A6 /* Separator.swift */,
			);
			name = Separator_ios;
			sourceTree = "<group>";
		};
		3EDEF6CAB8494E48664EFD15C5F42C42 /* TouchForwardingView_ios */ = {
			isa = PBXGroup;
			children = (
				BA610DA54C5958CA95C8A662D63B47CE /* TouchForwardingView.swift */,
			);
			name = TouchForwardingView_ios;
			sourceTree = "<group>";
		};
		49C7D9F6D15C4E4899B7F978274EBBD5 /* ResizingHandleView_ios */ = {
			isa = PBXGroup;
			children = (
				F2B2AA8A262B395B05D77D9EF2BCD17B /* ResizingHandleView.swift */,
			);
			name = ResizingHandleView_ios;
			sourceTree = "<group>";
		};
		4C68E9F1820C078AA29EBBBA695A148E /* PopupMenu_ios */ = {
			isa = PBXGroup;
			children = (
				59146327A33EEE4EC4A144BB2D95B04D /* PopupMenuController.swift */,
				278EC9370F396F8BCA5ACD3787243BA1 /* PopupMenuItem.swift */,
				6C1FA818AB4C90CA525B8452FE461345 /* PopupMenuItemCell.swift */,
				5ADCEA4BD476BAC9E7D27760C472A3F5 /* PopupMenuProtocols.swift */,
				C0A262F8DBAB0D0ADA5D35447063BA98 /* PopupMenuSection.swift */,
				22F5552064955997B1FF9FDBD6A4B122 /* PopupMenuSectionHeaderView.swift */,
			);
			name = PopupMenu_ios;
			sourceTree = "<group>";
		};
		51BC998EBC99C0A1FD35CBC4708CDB0B /* BottomSheet_ios */ = {
			isa = PBXGroup;
			children = (
				08BED95B2C9BA29F65F84CBCF901E136 /* BottomSheetController.swift */,
				17D47BFC91D1F0CDA499B5A0919725E1 /* BottomSheetPassthroughView.swift */,
				D9B6E733557E9DB456936CBA5C401B96 /* ContentHeightResolutionContext.swift */,
			);
			name = BottomSheet_ios;
			sourceTree = "<group>";
		};
		53422EE98B6814A7B0E7667255C8C274 /* AzureCommunicationUICalling */ = {
			isa = PBXGroup;
			children = (
				262D4FC1818F409DCBD3551A6542A6EE /* AccessibilityIdentifier.swift */,
				531EA0E8D01F61729B3C499E45B3840E /* AccessibilityProvider.swift */,
				71F4AAB750FB164B117EFD1F78D4AA9D /* AccessibilityProviderNotificationsObserver.swift */,
				C36108D33AF689B99BBE37A24F9A438B /* AccessibilityProviderProtocol.swift */,
				2F5448B6BDB3FB53E36B8A4142376E35 /* ACSCallEndReasonExtension.swift */,
				D61B29536A94347135E1D3D9762271EF /* ACSCallingStateExtension.swift */,
				9A71F69DDEA462A85DE3C9B58C0693ED /* ACSCameraFacingExtension.swift */,
				D51B69EE13AFC7B1105AED2DE0003747 /* ACSDiagnosticQualityExtension.swift */,
				269F416E43BE05BE3AEB15562335209C /* ACSParticipantStateExtension.swift */,
				BCEE3114197F3B1431067D326CC8A4C3 /* Action.swift */,
				2773551EC47EDAC57B6775E00C2ED394 /* AppLifeCycleManager.swift */,
				42318D37CA282796B604ABA8F9095C0C /* AppLifeCycleState.swift */,
				0301A4464AF0D7E255FAD06F2C5B8AE3 /* AppPhaseKey.swift */,
				B772B257C1BF436544C8E9075FE443AA /* AppState.swift */,
				0D0B3E335A380AA87D4AC3FB862125C6 /* AppStateReducer.swift */,
				1CAFE445C653E0AF4BD938517442C2F3 /* ArrayExtension.swift */,
				CFB59273F188B58A956814DA4FDBE61B /* AudioDeviceListView.swift */,
				E0EB44C551D89663740A9BFB89BACDFA /* AudioDevicesListViewModel.swift */,
				DC96DA053E2A69F84FE5C41A5EAC1470 /* AudioDeviceType.swift */,
				56CBA3E5071759DB2C12C47E68A7D883 /* AudioSessionAction.swift */,
				C5D00D63466046D0CC8CEC2C684AF872 /* AudioSessionManager.swift */,
				CF9561D60C46432EA43238A09800C510 /* AudioSessionReducer.swift */,
				408A3AE5333ED59ACF32EC58DBC49E03 /* AudioSessionState.swift */,
				11B8A2F72EA9743D1744DA44D2D9B93B /* AvatarViewManager.swift */,
				B3C7C3776A3DFC6AD9FCC27D5396DF44 /* BannerInfoType.swift */,
				13A1DAA31EA4071EE5EE8A87D8065E33 /* BannerTextView.swift */,
				F35BB53E051EDDF790EAA717C5AD3D7C /* BannerTextViewModel.swift */,
				8C9AB15AEFE48DF18268688F4459844F /* BannerView.swift */,
				7F452CF9330FB3E3E5A3427CD68B893E /* BannerViewModel.swift */,
				F3A7FB7B5BB557CFC57AC42DC63057DB /* BaseLocalizationProvider.swift */,
				CAFC82777C625A58E3DE6A302530E06B /* BottomDrawer.swift */,
				ED27BEB9573F9494E493ED01EF3332E6 /* BottomToastView.swift */,
				7BC176FA5AFAF40683ABDABF415FB721 /* BottomToastViewModel.swift */,
				D6E5D1979C95206398F3E37F61401690 /* ButtonState.swift */,
				37C9433A53B7FC888E96775D61B094DC /* ButtonViewData.swift */,
				AE3643C6FA971B88D53BEC7C54D9A909 /* ButtonViewDataAction.swift */,
				2FB7226D26E5565722E65CCB7A911307 /* ButtonViewDataReducer.swift */,
				F80174F9963D4E5D8DFCB82032F35648 /* ButtonViewDataState.swift */,
				3B096016243FC8A40EFCC8FDA5079DD5 /* CallComposite.swift */,
				708438BC86FAA596A5EA9E36EF56148A /* CallCompositeAudioVideoMode.swift */,
				ACD3A3EBB1C1EB3480DE9035D31868CD /* CallCompositeCallState.swift */,
				5B5BEFFA3B0DA6976BC05D13CB8AAA62 /* CallCompositeCaptionsData.swift */,
				F9E7C2367480F88523603EDCB00C7F35 /* CallCompositeDismissed.swift */,
				4E95D969C014886926D36D05A1028A7D /* CallCompositeError.swift */,
				7DFFCEB2678C46FEB76ECA7B86C58867 /* CallCompositeInternalError.swift */,
				B399BBBE8E4E00C64C8E28CF3BB81D68 /* CallCompositeOptions.swift */,
				DB57C5C0EC0087608DB222E64B01247B /* CallCompositeUserReportedError.swift */,
				063FADACC276E44058ACBAA52A01152E /* CallConfiguration.swift */,
				219FD1924126E1119F10E7B98DEF4763 /* CallDiagnosticsReducer.swift */,
				226FBEA519A8A38135A424309EDF2657 /* CallDiagnosticsState.swift */,
				39095A9B9926B6694C02C86037E9A96C /* CallDiagnosticsViewModel.swift */,
				1203542E28AB32D844C7AA6879500F4E /* CallDurationManager.swift */,
				1EAC9F4A1A49EF099462903C61D3A483 /* Caller.swift */,
				6D1C4DCEE12252D7285D284F6FD69114 /* CallError.swift */,
				279FB395473B3C0BE404C395A17EF437 /* CallHistoryRecord.swift */,
				F65DFAB111B4D66F81FD48CF18BEEFDA /* CallHistoryRepository.swift */,
				2928A6C141A717EDD662FED561A0AFCB /* CallHistoryService.swift */,
				0080BC482A77463BAA32B97A9315BDDC /* CallInfoModel.swift */,
				70F84D3362118FDE5C4F28877FDF3ADB /* CallingAction.swift */,
				99330688314D8B66BD7D2087C17C1B3E /* CallingMiddleware.swift */,
				E494AFE4F4CD3C7CA87384C7235B0ED9 /* CallingMiddlewareHandler.swift */,
				8D2A393468C1F2FFCE47A211112EDE4E /* CallingMiddlewareHandlerExtension.swift */,
				8EDFA7A885D3AE403DF6AAD89DC57623 /* CallingReducer.swift */,
				70C47B69E283EEB360F4B72D1B78E09B /* CallingSDKEventsHandler.swift */,
				2BAAD952B552D0690BFCA8C7E658B96D /* CallingSDKInitializer.swift */,
				75B10E1058C54DF613C799D1C9E2C464 /* CallingSDKWrapper.swift */,
				3BDB9A196109E2377F7B394312BA8411 /* CallingSDKWrapperProtocol.swift */,
				B9EDA7F6A7BE98B46DD1FCF9FB1C5BDD /* CallingService.swift */,
				58732D1B29C41808E4C37629F8FBAFB0 /* CallingState.swift */,
				95042D657EC783F2B920241557CC2247 /* CallingView.swift */,
				967F0E039F8EB390364AD9B5F3A2749B /* CallingViewModel.swift */,
				AF62620421CF1A8CC28D43CE04A10EFB /* CallKitOptions.swift */,
				E27C775AE1409D946C84E0E5A6090D9B /* CallKitRemoteInfo.swift */,
				205EA6DC83E93406D4D1CFEE1E3654FF /* CalllCompositeRttData.swift */,
				F311DCBB6CE9BF641EB4C151B7B1789A /* CallPipVideoViewController.swift */,
				B296179CD125E8BBFABA7FAFB11DFC9D /* CallScreenControlBarOptions.swift */,
				40502CAA76933496B20D68093A435F06 /* CallScreenHeaderViewData.swift */,
				17A0DD6A30532341B683365BFD727109 /* CallScreenInfoHeaderAction.swift */,
				9A92C037CBCD0B65A68610A1397E6ECB /* CallScreenInfoHeaderReducer.swift */,
				DA78D346CFEABC00DADEB7E40F34EAFF /* CallScreenInfoHeaderState.swift */,
				6F3940DD8AE1F12F6138213A990CCD7C /* CallScreenOptions.swift */,
				05247F25EE72B97582CFE38B8388E0AB /* CallStateManager.swift */,
				7DC7F22CF13B575C14DC2843CAD51A7A /* CancelBag.swift */,
				49C58831712EB325A9DB3AEA7E2CC6BA /* CapabilitiesChangedEvent.swift */,
				20D544825A928489013B8802AB174DD5 /* CapabilitiesChangedNotificationMode.swift */,
				306AD77AF822E2EA0EDEE71676243865 /* CapabilitiesChangedReason.swift */,
				5E6E68698D479A4417ACEF986F8120CB /* CapabilitiesManager.swift */,
				D4D2597860D550CA0FA0D7917A92EF5D /* CapabilityResolutionReason.swift */,
				32D9E6D248A44E29466AEBE3E6C16868 /* CaptionOptions.swift */,
				74B299A2D09F2578C65AB73C731B5763 /* CaptionsAction.swift */,
				13C5844C405EF38D94250F837994ED92 /* CaptionsAndRttLandscapeView.swift */,
				6531D67EE076410ADD78162639954F4C /* CaptionsErrorView.swift */,
				BA5065ECA2F7D34771CAE78F77D368DD /* CaptionsErrorViewModel.swift */,
				C1B3E2B0B90D1A7BFD51C6D889DBCF74 /* CaptionsLanguageListView.swift */,
				3C0AAB679727109EC948F9C34DF367DF /* CaptionsLanguageListViewModel.swift */,
				29B80483762E8C6394430ECED0E38DAD /* CaptionsReducer.swift */,
				60EB9D9E66A3F6F6812B402F23CF6D72 /* CaptionsRttDataManager.swift */,
				194EE3B4F2200717B8907E9424B79BFC /* CaptionsRttInfoCellView.swift */,
				D9347FFA384836ED2CFC477F03046E68 /* CaptionsRttInfoView.swift */,
				CFED14DAFEAC58EF0FF13D249279019C /* CaptionsRttInfoViewModel.swift */,
				CD7CCB5B049008FD2F13E68BF4665117 /* CaptionsRttListView.swift */,
				7C613D393C7137E783637A2A89D73B87 /* CaptionsRttListViewModel.swift */,
				0274128AB684CCAC7D06822C0FBA06F0 /* CaptionsState.swift */,
				E2E998DD66E9652A98A669608F1AE986 /* ColorExtension.swift */,
				6531966EFBFDC7768369081A621DE586 /* ColorThemeProvider.swift */,
				6D5130D0FB0CEBC82210A8F669CD58F6 /* CommunicationTokenCredentialError.swift */,
				596F9D63F60626040AEC01E09924B2CA /* CompositeAvatar.swift */,
				EC4A373D3270D2DEF3C8658460600A61 /* CompositeButton.swift */,
				172690718843666EDCCE06A30502DE11 /* CompositeErrorManager.swift */,
				7EEC83AA5B34B5CA3012B756A68737FB /* CompositeExitManager.swift */,
				E7F42A5449586A5CDF7FFCC2613B8386 /* CompositeViewFactory.swift */,
				7B0BF5CF531B79E2A1F160893249238E /* CompositeViewModelFactory.swift */,
				0A7ABE8351935FEC3F42FA5CC821B91F /* CompositeViewModelFactoryProtocols.swift */,
				21622B3E2C0FB25D714EFA3E4920CE83 /* ContainerUIHostingController.swift */,
				BF8CA1902A3E746395C999BCE8E1AB5A /* ContainerView.swift */,
				5A563B17F12AA0B0DC4D1E5C448A1236 /* ControlBarView.swift */,
				4352CBF9DBC48AF71723F338BAD43CDD /* ControlBarViewModel.swift */,
				1B6353F50701D842A002FF9B8E859D1B /* CustomAlert.swift */,
				0841F2E163FBD3258C7CFEA3CF3FBBE3 /* CustomButtonViewData.swift */,
				31E0CF6DAF1E84826C634A18A60954B4 /* CustomTextField.swift */,
				84BB5B98C93B913663B1A4BF3E5D9016 /* DebugInfo.swift */,
				544338A618970DEA569F4C6A0A511B33 /* DebugInfoManager.swift */,
				95C6FA52F3EA47D073C9052E26172911 /* DebugInfoSharingActivityViewModel.swift */,
				D000FFF6214A0D59E84174F78D66A4A5 /* DefaultUserState.swift */,
				6A52F633C7D0F66B3802222AECCF3651 /* DeviceExtension.swift */,
				0B12CC5A0B15BCA3FED5ED7A7125AD77 /* DiagnosticConfig.swift */,
				3067ACAD5DF9C145E107981C11CC3883 /* DiagnosticsAction.swift */,
				193F59C267BDC2954DCBD29D8FDDD40F /* DraggableLocalVideoView.swift */,
				AE490866223E2BA6FC2DC3A2BC2D436E /* DrawerBodyTextView.swift */,
				2117D4B5A8620CB0095849CCB6E80C47 /* DrawerGenericItemView.swift */,
				AB133885A3F907D1DF292B6DBE0ED66E /* DrawerListView.swift */,
				7EF2FD88407950F3C079382A97AE767E /* DrawerParticipantView.swift */,
				617DA3CBB02D35BAC9844F8D6FDE5AF0 /* DrawerSelectableItemView.swift */,
				F664E0FD672EC99E6B911E2754BDBCD6 /* DrawerTitleView.swift */,
				5DD289A3EB5470C361F1E553F4B91AD3 /* DrawerViewControllerProtocol.swift */,
				027F46675C7F287E56408C08C15C73D5 /* DrawerViewModels.swift */,
				8FF3887B3B601C3932BE9AA47D95F7D2 /* EnvironmentValuesExtension.swift */,
				CEF302F2865D5EF55FFCF3E9F55120F7 /* ErrorInfoView.swift */,
				33DB302B50426EF421D2F53601912405 /* ErrorInfoViewModel.swift */,
				E5A60CAF27A6D6477DC41D42EF66FEC2 /* ErrorReducer.swift */,
				37B2217166ECA8A7E581A68CB86867C4 /* ErrorState.swift */,
				97B42ED0F4919871EE735A996465EFB2 /* ExpandableDrawer.swift */,
				A6876A1C4E1E76EBF351C36543892A55 /* FontExtension.swift */,
				041A9546BC831DD4903B886FB8B28807 /* Icon.swift */,
				FBD6D72FCC3C0A4D97D30AA94B92A02F /* IconAndLabelConversion.swift */,
				9F095010ACD14AEE4E2B4CD0EF4379B4 /* IconButton.swift */,
				5F9E74FAE941FA03F3D71F803B2F2551 /* IconButtonViewModel.swift */,
				063677845578DE45849A2145570283C9 /* IconProvider.swift */,
				2BAE8250C171D3BA639B7D7B97F47011 /* IconWithLabelButton.swift */,
				B7858A523D8CE7E9B32B3AD2E0FF1277 /* IconWithLabelButtonViewModel.swift */,
				38A559F770896C057085120739844D3E /* IncomingCall.swift */,
				7A9A6D03F74274EE6AEDBF0CB25CA7A9 /* IncomingCallCancelled.swift */,
				B057843310E2BC4D1A11681D2B3D0C89 /* IncomingCallError.swift */,
				7724B3604CF20668E6B53313DDC88CF3 /* InfoHeaderView.swift */,
				E2C73009B2782FA4E19C89ECE8DE42BA /* InfoHeaderViewModel.swift */,
				90D50155493B61A0569090870A2135DC /* JoiningCallActivityView.swift */,
				59F3D5210DA73FA0305605C65598A66C /* JoiningCallActivityViewModel.swift */,
				CBA0AFA18700F0D16059DFC2D214E346 /* KeybaordResponder.swift */,
				47DF492CCAC5D5667C4D1ADBAB7C6D38 /* LandscapeAwareKeyboardWatcher.swift */,
				95F4C75EFEE51C98C333DE6B870DE5AB /* LeaveCallConfirmationMode.swift */,
				35A615C7ADDD5851E814B5CF71FA1C99 /* LeaveCallConfirmationView.swift */,
				4B951D8B6D4294F7E0A70D7BFCF0CEDA /* LeaveCallConfirmationViewModel.swift */,
				6135E9D116F377CA1936C39AC919E014 /* LifecycleAction.swift */,
				A0916B7508E92C6994419DEA44DBD4EF /* LifeCycleReducer.swift */,
				82EF81BA07C5A8CCD7C478E6326FCC03 /* LoadingOverlayView.swift */,
				43F1F53555D656DD229B1B798CABEA87 /* LoadingOverlayViewModel.swift */,
				51853927E49C4E82CA25617521D8688F /* LobbyErrorHeaderView.swift */,
				2F7791E220BF4028BC05FE70D69E9090 /* LobbyErrorHeaderViewModel.swift */,
				CB511B1F8C4FA9EA40D159DC863E075E /* LobbyOverlayView.swift */,
				4CA8D88AB94D8AD39F264A7F56D2A884 /* LobbyOverlayViewModel.swift */,
				7B2BDF2CB10073A7997A16204BDD133D /* LobbyWaitingHeaderView.swift */,
				CE62285A8986240D418B928205FB0F8B /* LobbyWaitingHeaderViewModel.swift */,
				EADD14CE206148D922688699F4B0C66E /* LocalizationKey.swift */,
				A6868E914AD372E6D5CC11202578F453 /* LocalizationOptions.swift */,
				2902D8EE5D7ADF5746BFADA4C69D948F /* LocalizationProvider.swift */,
				C92E0C6D24CD0990BDFDB37080FFC7AC /* LocalOptions.swift */,
				4C096B3D9CE8B83F8C5468C5FAB40A6D /* LocalUserAction.swift */,
				4087E556F3E09E569E812BC87B799984 /* LocalUserReducer.swift */,
				BC957AE6D4DB18DECF63614A413BA7A6 /* LocalUserState.swift */,
				5CD6AE56E1F1FEA7A07966D3AC952D93 /* LocalVideoStreamExtension.swift */,
				69A1C089943204C4AE2DAC38A62D12AA /* LocalVideoView.swift */,
				66B83E0A984344AF9E317FECA71A1DEC /* LocalVideoViewModel.swift */,
				BC00BF7CD36D080BCC03D5345BDC8E1F /* LockPhoneOrientation.swift */,
				998E99C627AA67517378F54138BAEC82 /* Logger.swift */,
				72E11C4258EA6DFAB8A1CC19A677796A /* MappedSequence.swift */,
				6E9435372F287B5673FBBAB771DEB5B1 /* MessageBarDiagnosticView.swift */,
				D1529838C2753DB8D0A725CB14A34C0D /* MessageBarDiagnosticViewModel.swift */,
				068739BFD2E8849C70391A0DE1E27E17 /* Middleware.swift */,
				16F7A9061740284EA7D9EB068AC365CB /* MoreCallOptionsListView.swift */,
				B1E28F5E472EE59C17A2F72ADB8B8219 /* MoreCallOptionsListViewModel.swift */,
				AB8C8B95709332F1A537618BAD51FB77 /* NavigationReducer.swift */,
				CC75BD097424C53906FABBAE47E3893A /* NavigationRouter.swift */,
				AB1A4903AA79B06795C4542AB7485FB1 /* NavigationState.swift */,
				9450C7EF18073872DF8B0BF20E01AF74 /* NetworkManager.swift */,
				CF49763E8F89C09177863A3ABAAA9958 /* NotificationCenterName.swift */,
				673643236C52F7E2127858CE0D342842 /* OnHoldOverlayViewModel.swift */,
				F3ACAA6E7964BD6B4971927811F1BA4B /* OrientationManager.swift */,
				87D3BB8D380E82C2DC20476E49EC67BA /* OrientationOptions.swift */,
				9882C331297D666DF83612C2A9BC4337 /* OrientationProvider.swift */,
				9BBE34CC70AFD621A825E6A45E886DE3 /* OverlayViewModelProtocol.swift */,
				A0BA07FCACB4449236F9D2748E2B97C2 /* ParticipantCapability.swift */,
				A68E49E62ACD9C3D5DEEB9572C3E4974 /* ParticipantCapabilityType.swift */,
				470E3141B7AF49108835912477BE1C6C /* ParticipantGridCellVideoView.swift */,
				BFFA8F5146A63328959236141F803FCA /* ParticipantGridCellView.swift */,
				2C94F2B36E6A2FDCFE522B3E6B2D4238 /* ParticipantGridCellViewModel.swift */,
				91E7ABAEF8120BD170CA30ACFC5D8F1B /* ParticipantGridLayoutView.swift */,
				DFEA35640AC140925F2160D468256639 /* ParticipantGridView.swift */,
				44C69948F42ADCBF5C82B5B7AA3E4012 /* ParticipantGridViewModel.swift */,
				144FA1D5AC01C594738AE993AFE3FB17 /* ParticipantInfoModel.swift */,
				7E6942AC18DB9B758098A0C2AF30DE35 /* ParticipantListVIew.swift */,
				4AC2A6809879809D48466875C2616841 /* ParticipantMenuView.swift */,
				E1F4895CC34CF03EFD122E0BBBF3421B /* ParticipantMenuViewModel.swift */,
				B77633AC8AEBFB5CF5825354030B78F9 /* ParticipantRoleEnum.swift */,
				DB744CF8EDA2762E0B6DB6E8F39926D7 /* ParticipantsListCellViewModel.swift */,
				FA8D716813D3130FA8106B1CBC836973 /* ParticipantsListViewModel.swift */,
				12C368BF65434904B90FCAF82BA91ABC /* PermissionAction.swift */,
				3C36F6C6E5D1925404A3822BD531E6AA /* PermissionReducer.swift */,
				8A1C1AE8A53E82FBBBE179D3147C2E2C /* PermissionsManager.swift */,
				5FE547F9EF02B7E6F687256F19E8E695 /* PermissionState.swift */,
				C2B29DCE4D2A947D29A24C56E0D75F90 /* PipManager.swift */,
				684829E58B8B328DFBB2C1CB8B1BC2E6 /* PipReducer.swift */,
				1D902CC62F7F2FED9BD8D043C4B9148D /* PopupModalView.swift */,
				6860143A03DE7807AF0E4BC47E73FA00 /* PreferenceKey.swift */,
				82B588EE471DD4863C4F3E1827B75F16 /* PreviewAreaView.swift */,
				A0137455B49A97371106A1571801083C /* PreviewAreaViewModel.swift */,
				EDD93D840930CB9A58E7C2948A203B73 /* PrimaryButton.swift */,
				55119FCF266D0C9EBFBB240A6D46C87C /* PrimaryButtonViewModel.swift */,
				36029DB0C38EEBA6052EB14C8E6E52BE /* PushNotification.swift */,
				35EB4DF2FFAC5624054E8EFC279C9DAA /* PushNotificationEventType.swift */,
				A8D08B31B1F8B137E16E26B0112CC47A /* Reducer.swift */,
				357A6C7BC1E1B1456C3B944CC0AA7556 /* RemoteOptions.swift */,
				5CB2963F6976CB6B1583C80139DADA21 /* RemoteParticipantExtension.swift */,
				06119083CF4415F895393C2B26555F91 /* RemoteParticipantsAction.swift */,
				383D8B37BAA305E3CAC8AAECE10FC9EA /* RemoteParticipantsEventsAdapter.swift */,
				6B1DF1EF3DB9D83F0114F0D5CA7D7E97 /* RemoteParticipantsManager.swift */,
				69F87B3A7FD09BB690412D6EC10FF1C8 /* RemoteParticipantsReducer.swift */,
				EA418E2843D9E62F06D607FC0724031C /* RemoteParticipantsState.swift */,
				FDDFF5CD8018A225E1C89D80901744D5 /* RttAction.swift */,
				89E6D13DD0567DF3585578CE6EBEB112 /* RttReducer.swift */,
				2B251147CB69E271004FEC65D997D86F /* RttState.swift */,
				04D9C539A6E4A7FF5C0C554EA23FC732 /* SetParticipantViewDataError.swift */,
				B258201F04C519FB5DAE449AC0F41F17 /* SetupControlBarView.swift */,
				58AE038C337B203EB262A041A02C5534 /* SetupControlBarViewModel.swift */,
				26AD500ECC3D9CD8C0C6F55114F085A8 /* SetupScreenOptions.swift */,
				30F59FD319397F7F3BF782E2A0E6FB84 /* SetupView.swift */,
				5ECE18B19EFA2A761E3CB028CE0B500C /* SetupViewModel.swift */,
				C0712082E8DD32619C6C19BF72BB19C8 /* SharingActivityContainerController.swift */,
				A1A0C3BC83D750EC2B24F0A4B63CA13F /* SharingActivityView.swift */,
				A3206EBFBBC3FB28B0166ACBBCCE1394 /* SourceViewSpace.swift */,
				943A8EDFF888C1E9C1DF659C2A972A46 /* Store.swift */,
				852CB6DF7B4D54E2BB7CC8B4BBB94717 /* StoreExtensions.swift */,
				50DCAD23FC62D0A29CFD2593BA42E2DA /* StringConstants.swift */,
				A604BE74E03E15058F25D4F54BCE28C7 /* StyleProvider.swift */,
				C55567F7C85C976E5A389C0BFF8D6830 /* SupportedLocale.swift */,
				DA553BDCA19BCAC7527E3CA1BAD9D519 /* SupportFormView.swift */,
				6C60F13B32E165751CA8F4718EA71D2A /* SupportFormViewModel.swift */,
				55E6A5A541EDA812FA06386F0549BB33 /* ThemeColor.swift */,
				4F88B9F46FDE2365645E2076AA1AC802 /* ThemeOptions.swift */,
				7B6FFBE2E8EBA1794FB724F48964DB51 /* ThrottleMiddleware.swift */,
				98628DE2E3D3A0E442AA61B799429A8D /* ToastNotificationAction.swift */,
				A58FA4FEE4F8BC380EFA302A5425FBA6 /* ToastNotificationReducer.swift */,
				99C09043F87A7021A9829A31FE06303D /* ToastNotificationState.swift */,
				8CEF10B711B3B51BCCD4B22AD92714D6 /* UIViewControllerExtension.swift */,
				ACE425916CFC4C433A43D6426AA53C57 /* UIWindowExtension.swift */,
				D17750201604D3C0371C403D9623B9B3 /* UpdatableOptionsManager.swift */,
				4E281BD3A0DD63139190CA6BD921C536 /* UserFacingDiagnosticModel.swift */,
				6E31637A3AC15FC5289F1CE3FCE2BC7A /* VideoRenderView.swift */,
				6C92BEB6651D97E4A6A379DD09193612 /* VideoStreamInfoModel.swift */,
				7585D83C3D2E9F1E0B576CB6095C0EE7 /* VideoViewManager.swift */,
				9DFBD9606077F980791A7E453C7B80C2 /* ViewExtension.swift */,
				9FFC41A5A1944F5755936835A4FDEC1C /* ViewModifer.swift */,
				18659C0FC416310F1B04064FAFA4A419 /* VisibilityAction.swift */,
				F6F4C386E58F309333EF74C5DD9450D0 /* VisibilityState.swift */,
				EA9D5783068A0AA7BFF369C10160564C /* ZoomableVideoRenderView.swift */,
				AE648255D5AF11302C535D8D665A6B17 /* Resources */,
				57172349934425F94EF87EFAC76FBBD7 /* Support Files */,
			);
			name = AzureCommunicationUICalling;
			path = AzureCommunicationUICalling;
			sourceTree = "<group>";
		};
		57172349934425F94EF87EFAC76FBBD7 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				DDBE1C2D993E59E9BBFFF6534CA38BF8 /* AzureCommunicationUICalling.modulemap */,
				8C9D31FF9ADCCA290A6255170F20A809 /* AzureCommunicationUICalling-dummy.m */,
				E553BAEE1695E7E48C3A17EB5218302E /* AzureCommunicationUICalling-Info.plist */,
				A9103F6802C79CD9BF700B60D9F655D8 /* AzureCommunicationUICalling-prefix.pch */,
				4FF705F9C575520FF9FD76F5C881C6DC /* AzureCommunicationUICalling-umbrella.h */,
				238398EADAF89E6189D854EB9582F9B8 /* AzureCommunicationUICalling.debug.xcconfig */,
				425529FC72F1D78C2518CD971694D55D /* AzureCommunicationUICalling.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AzureCommunicationUICalling";
			sourceTree = "<group>";
		};
		5E06BABC4DCE9842129A40F380D3F14D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				6A96D349B46E3A2E79875386F0B61590 /* AzureCommunicationCalling-xcframeworks.sh */,
				240C73B422C7877840DAEBD4742F091D /* AzureCommunicationCalling.debug.xcconfig */,
				4942C9B87F243C0F84EF40A55FD95C73 /* AzureCommunicationCalling.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AzureCommunicationCalling";
			sourceTree = "<group>";
		};
		61BC30C0B26758D9C1DA99B22776222D /* Pods-iOSProject */ = {
			isa = PBXGroup;
			children = (
				EDE99E3552F3017D5E5DBE987A399E75 /* Pods-iOSProject.modulemap */,
				EBADC2E0AE01DD4A333262E4D64F8858 /* Pods-iOSProject-acknowledgements.markdown */,
				8996DBCF82707F4C5F1314D6B1E64263 /* Pods-iOSProject-acknowledgements.plist */,
				FA20A5B2176EB4E24B773E4C3B84D377 /* Pods-iOSProject-dummy.m */,
				CF7873184DCF81262BF51506E548042F /* Pods-iOSProject-frameworks.sh */,
				381B586F9BEBB3395D53891762198DC9 /* Pods-iOSProject-Info.plist */,
				713AE5DD6CF53E0A12CA3B0630A402E7 /* Pods-iOSProject-umbrella.h */,
				9275DCC7B88C1F257EFB7A5BC59AFFF2 /* Pods-iOSProject.debug.xcconfig */,
				C211D9644098BE447DD459B8CD561AC4 /* Pods-iOSProject.release.xcconfig */,
			);
			name = "Pods-iOSProject";
			path = "Target Support Files/Pods-iOSProject";
			sourceTree = "<group>";
		};
		66898DC859E5B0665D7E97B23D52A2C8 /* ActivityIndicator_ios */ = {
			isa = PBXGroup;
			children = (
				E6BD320AE43B783EDEBFBFD37F071226 /* ActivityIndicator.swift */,
				EE826D52AF3C62D88E8915C5414A6D0C /* ActivityIndicatorModifiers.swift */,
				74EAFBA1A72000C286263A66C76F2AC6 /* ActivityIndicatorTokenSet.swift */,
				A31114F90D8DAD40C5677C1B8BC5DE4B /* MSFActivityIndicator.swift */,
			);
			name = ActivityIndicator_ios;
			sourceTree = "<group>";
		};
		732651C12C2AB6C621F6C65273AAB2F8 /* SwipeActions */ = {
			isa = PBXGroup;
			children = (
				7F722929799BB6727D9F120813E18DC5 /* MeasureSizeModifier.swift */,
				389CABF0F421788C4E7E1DCD4F5ABA50 /* SwipeActionModifier.swift */,
				C084400BE0A523B80129C509BBD7C214 /* SwipeActions.swift */,
				06FC2068C2D9B7937C846DE558DD988C /* ValueChangedModifier.swift */,
				FB8C2A703454FABD05E3132E2DA8609A /* Support Files */,
			);
			name = SwipeActions;
			path = SwipeActions;
			sourceTree = "<group>";
		};
		733A9990CABE301ECDFE79507C2F41C8 /* TableView_ios */ = {
			isa = PBXGroup;
			children = (
				4EC4254B28BB6C1CF9A2A313350DD2F3 /* TableViewCell.swift */,
				D1EE3C1452BD5D657E2171DF60B8113D /* TableViewCellTokenSet.swift */,
				DF4EE540C6CDF62F03E8B54F4860C8B7 /* TableViewHeaderFooterView.swift */,
			);
			name = TableView_ios;
			sourceTree = "<group>";
		};
		73D893CC5127CD35E292D40C52920DF4 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				61BC30C0B26758D9C1DA99B22776222D /* Pods-iOSProject */,
				D5E69D38E8DCBE43BB98D0CD611A79C0 /* Pods-iOSProject-iOSProjectUITests */,
				C77E903762FF67D68706786E4DBC74CE /* Pods-iOSProjectTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		850849C278F6FF9F36B0BD20E23CC9F6 /* Avatar_ios */ = {
			isa = PBXGroup;
			children = (
				5A805D016E85F90354680EFC21A160FF /* Avatar.swift */,
				29E57BFA29CF2B403BED3DB1BD83FDA1 /* AvatarModifiers.swift */,
				9BBFD847342BE87933D544A2FFD66F91 /* AvatarTokenSet.swift */,
				177F54C04B7D49F51BF95447C8F0730E /* CircleCutout.swift */,
				3992DC8F2E3FA6345E28F02D0C9B4979 /* MSFAvatar.swift */,
				9DC04648E59A69FC8269BFA175E45DB9 /* MSFAvatarPresence.swift */,
				421BAA4C8B9F1E47B66491B84536D33D /* Persona.swift */,
			);
			name = Avatar_ios;
			sourceTree = "<group>";
		};
		850E3A13D55EF81A219FFE9C6592FE95 /* AzureCommunicationCommon */ = {
			isa = PBXGroup;
			children = (
				5FC4615733668365907B297184444CD4 /* AutoRefreshTokenCredential.swift */,
				4284C28FAE7380E750A92A818ADD0B2C /* CommunicationAccessToken.swift */,
				32E5AB2D1D4125269E7EFD3A59E27FF3 /* CommunicationCloudEnvironment.swift */,
				3A1ABC86FF6BE6569A82921588B3E402 /* CommunicationTokenCredential.swift */,
				D032AFA98F2B30BDC8E96F4147BD4B62 /* CommunicationTokenCredentialProviding.swift */,
				989E4D12CE4C5DAD41D76D4F9C90111B /* CommunicationTokenRefreshOptions.swift */,
				3B0C855E6E711B3B54F4B8F1FD2B9718 /* Identifiers.swift */,
				43B49E5D2505CBF81E0779096790AE01 /* JwtTokenParser.swift */,
				56421BBE1E308F6C975EF9D76BE8B499 /* StaticTokenCredential.swift */,
				BEC46B7DA2536B8F8F3B00D521A8E3E6 /* ThreadSafeRefreshableAccessTokenCache.swift */,
				2F077EB93A5559349C2844F65BE3D393 /* Support Files */,
			);
			name = AzureCommunicationCommon;
			path = AzureCommunicationCommon;
			sourceTree = "<group>";
		};
		922FD0818D33F8CB3B5877A8C0B43767 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1633885AE7332E24C809481268E3D698 /* AzureCommunicationCalling.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A21E6B1FFFB3DAA99078FB457E174A77 /* AzureCommunicationCalling */ = {
			isa = PBXGroup;
			children = (
				7A591602F63815725832B77BDEA394BD /* ACSCallKit.h */,
				7E7C148B1A4DB6A6C6682CAA4E95EDF3 /* ACSCallKit.h */,
				1C18A8AD2EFDFB837E57DA856DDACFC5 /* ACSFeatures.h */,
				CC065F129F325F4ADBD1ED5F7CAD8468 /* ACSFeatures.h */,
				EABA0734D2A5CC5FB990DD568C7D5F1A /* ACSStreamSize.h */,
				0696668008360E15891ACE87989427E0 /* ACSStreamSize.h */,
				2BF668215FADF3B714F4718F3791FAF6 /* ACSVideoStreamRenderer.h */,
				042096D1657727F5DC351E684915A1B8 /* ACSVideoStreamRenderer.h */,
				90F5FB9F4C5A96A44C1C62CED232C492 /* ACSVideoStreamRendererView.h */,
				410855F1FB1F9DD85D16DCE13B7562C1 /* ACSVideoStreamRendererView.h */,
				972E6D3F0789D216CB19876FD3D3E78D /* AzureCommunicationCalling.h */,
				53C702B517D23933A9D72B85B441E008 /* AzureCommunicationCalling.h */,
				D2EFF46B88A977C51D8D2AE18F9D8678 /* AzureCommunicationCalling-Swift.h */,
				E91C7357AB187817E0734576154542B8 /* AzureCommunicationCalling-Swift.h */,
				922FD0818D33F8CB3B5877A8C0B43767 /* Frameworks */,
				5E06BABC4DCE9842129A40F380D3F14D /* Support Files */,
			);
			name = AzureCommunicationCalling;
			path = AzureCommunicationCalling;
			sourceTree = "<group>";
		};
		AB12D332A5F3B4967CBB3FA36466ADB6 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				3C4993DE15FB4CA19E6DF78F20D8B5D9 /* AzureCore.modulemap */,
				5872E993C44A65010BE1315D3960CF5A /* AzureCore-dummy.m */,
				949699B2A0BE63450DD8759B83341BAB /* AzureCore-Info.plist */,
				08976A16DD59F1122A20227BEC83FCBF /* AzureCore-prefix.pch */,
				FB3BA383AFAC1F032391FEBC4CFC0EE5 /* AzureCore-umbrella.h */,
				E9FD78EC0FD1B3A8E86D82BFE118A7C3 /* AzureCore.debug.xcconfig */,
				13935B8E8FEF162E9E7A0E9846F97225 /* AzureCore.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AzureCore";
			sourceTree = "<group>";
		};
		AE648255D5AF11302C535D8D665A6B17 /* Resources */ = {
			isa = PBXGroup;
			children = (
				102608995FA3ECFBF65D8E15E4ABFD07 /* Assets.xcassets */,
				94F313C6263B4527440AEF93A30811F9 /* Localizable.strings */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BB32657E7970ACFBC62F7E457FBC46D5 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A21E6B1FFFB3DAA99078FB457E174A77 /* AzureCommunicationCalling */,
				850E3A13D55EF81A219FFE9C6592FE95 /* AzureCommunicationCommon */,
				53422EE98B6814A7B0E7667255C8C274 /* AzureCommunicationUICalling */,
				23F94FB6CD10DE8799178EE5E36A5A44 /* AzureCore */,
				DA984AB6E0BCA1522A6C904AC73F2BD0 /* MicrosoftFluentUI */,
				732651C12C2AB6C621F6C65273AAB2F8 /* SwipeActions */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		C77E903762FF67D68706786E4DBC74CE /* Pods-iOSProjectTests */ = {
			isa = PBXGroup;
			children = (
				FE6AEDF4510C9D581D5977A9E57F0A08 /* Pods-iOSProjectTests.modulemap */,
				B01E2E468B6311C25B86AF5CC1B5E291 /* Pods-iOSProjectTests-acknowledgements.markdown */,
				61ADA42FF52C12B03D85EB219A9664F5 /* Pods-iOSProjectTests-acknowledgements.plist */,
				4C253A2EB0B95B3FD1062CED3E36E95D /* Pods-iOSProjectTests-dummy.m */,
				0D26A324599FE6B579AA01C7DC68A29B /* Pods-iOSProjectTests-Info.plist */,
				C399A358BC2C490D90AC384CADCDCD7E /* Pods-iOSProjectTests-umbrella.h */,
				2DE0809D6E8F91E5D2CD6A5099D53334 /* Pods-iOSProjectTests.debug.xcconfig */,
				5633990505F9B3ADD75173ED25153A26 /* Pods-iOSProjectTests.release.xcconfig */,
			);
			name = "Pods-iOSProjectTests";
			path = "Target Support Files/Pods-iOSProjectTests";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				BB32657E7970ACFBC62F7E457FBC46D5 /* Pods */,
				E5C4A4AD4A92BCF9ADBF2D4DB73CEE9D /* Products */,
				73D893CC5127CD35E292D40C52920DF4 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D5E69D38E8DCBE43BB98D0CD611A79C0 /* Pods-iOSProject-iOSProjectUITests */ = {
			isa = PBXGroup;
			children = (
				91E544D27ECBE7743545D0E4D38D108A /* Pods-iOSProject-iOSProjectUITests.modulemap */,
				2A6B92252CF785FBCE4AF821371897AD /* Pods-iOSProject-iOSProjectUITests-acknowledgements.markdown */,
				4AA4D55C12E766693D1C1C61EBFD5857 /* Pods-iOSProject-iOSProjectUITests-acknowledgements.plist */,
				6C06ECB67B0B0C5AA3FC10215D17D7D2 /* Pods-iOSProject-iOSProjectUITests-dummy.m */,
				2399421409BFB50267C72BF62792446D /* Pods-iOSProject-iOSProjectUITests-frameworks.sh */,
				2F610B67E35AA835DEDE2858EF7D6C9E /* Pods-iOSProject-iOSProjectUITests-Info.plist */,
				83E02E02FB925819D7301762052BCC4A /* Pods-iOSProject-iOSProjectUITests-umbrella.h */,
				DB9FDF128C332399D1C6B509AFA9ABD2 /* Pods-iOSProject-iOSProjectUITests.debug.xcconfig */,
				410EC546636B376A22B33FB306BEB539 /* Pods-iOSProject-iOSProjectUITests.release.xcconfig */,
			);
			name = "Pods-iOSProject-iOSProjectUITests";
			path = "Target Support Files/Pods-iOSProject-iOSProjectUITests";
			sourceTree = "<group>";
		};
		D9E6E314D3FCB2170F19EAE57F0CAD71 /* Button_ios */ = {
			isa = PBXGroup;
			children = (
				E1514DA8A0B7A198B45367C7F44BF6D6 /* Button.swift */,
				B22C9C500FE4BDFC523CD4534BFCC59A /* ButtonTokenSet.swift */,
			);
			name = Button_ios;
			sourceTree = "<group>";
		};
		DA984AB6E0BCA1522A6C904AC73F2BD0 /* MicrosoftFluentUI */ = {
			isa = PBXGroup;
			children = (
				66898DC859E5B0665D7E97B23D52A2C8 /* ActivityIndicator_ios */,
				850849C278F6FF9F36B0BD20E23CC9F6 /* Avatar_ios */,
				1149626FB9F98B855D7758629504A397 /* AvatarGroup_ios */,
				51BC998EBC99C0A1FD35CBC4708CDB0B /* BottomSheet_ios */,
				D9E6E314D3FCB2170F19EAE57F0CAD71 /* Button_ios */,
				F0143A214510BF31EF934CEA5C087DAD /* Core_ios */,
				E05002E7F37336F50A36327B9203D0DB /* Drawer_ios */,
				0EDB929F00F24DE9042FB6C788AE30C1 /* Label_ios */,
				EDD72CA3DCA3423799442B1084C55EC6 /* Obscurable_ios */,
				4C68E9F1820C078AA29EBBBA695A148E /* PopupMenu_ios */,
				49C7D9F6D15C4E4899B7F978274EBBD5 /* ResizingHandleView_ios */,
				3D53EF32748B442F858BE62476A2CF00 /* Separator_ios */,
				DF02AB28356D316EB006D3438EDD31E1 /* Support Files */,
				733A9990CABE301ECDFE79507C2F41C8 /* TableView_ios */,
				3EDEF6CAB8494E48664EFD15C5F42C42 /* TouchForwardingView_ios */,
			);
			name = MicrosoftFluentUI;
			path = MicrosoftFluentUI;
			sourceTree = "<group>";
		};
		DF02AB28356D316EB006D3438EDD31E1 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				04CBF581A99090BE8FA2F56860F8FED9 /* MicrosoftFluentUI.modulemap */,
				F66AA86C0D9004E91FE8FA2AAB29BED1 /* MicrosoftFluentUI-dummy.m */,
				F7D2845663B59084595D059B1ABDEC40 /* MicrosoftFluentUI-Info.plist */,
				5A14D44B69885CB8904DC3328DE82C75 /* MicrosoftFluentUI-prefix.pch */,
				57531BACE40433640AE941D2E9A63956 /* MicrosoftFluentUI-umbrella.h */,
				5E835ED93C3C8CFF613B0201B069C57E /* MicrosoftFluentUI.debug.xcconfig */,
				6B670DC714CFE9043492068C3CD54B8E /* MicrosoftFluentUI.release.xcconfig */,
				DE765ED433C81E1C1195A5D48BAF816D /* ResourceBundle-FluentUIResources-ios-MicrosoftFluentUI-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/MicrosoftFluentUI";
			sourceTree = "<group>";
		};
		E05002E7F37336F50A36327B9203D0DB /* Drawer_ios */ = {
			isa = PBXGroup;
			children = (
				CE65509D035ACE1E4A3C088E261B1328 /* CALayer+Extensions.swift */,
				873D32D3C85C8CF0AAA3E6429F065BF1 /* DrawerController.swift */,
				AC3E45754487C92C098E3F8482907685 /* DrawerPresentationController.swift */,
				F2559CE8EBDC6304277729C5FD5B62B5 /* DrawerShadowView.swift */,
				A4B660B5DAB93F47D1B002FF8327DFEF /* DrawerTransitionAnimator.swift */,
			);
			name = Drawer_ios;
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		E5C4A4AD4A92BCF9ADBF2D4DB73CEE9D /* Products */ = {
			isa = PBXGroup;
			children = (
				046BE0A5D32340465034A0DF75B74940 /* AzureCommunicationCommon */,
				C9C0E58C836CEECECBBA614EEAE53DC2 /* AzureCommunicationUICalling */,
				2A0569FC6542319D29A5F4C7D66F6F5C /* AzureCore */,
				479B603E5C8EAD405D4DCA6344A9E171 /* MicrosoftFluentUI */,
				419FF83B5DE08258908F2BF54EA6B771 /* MicrosoftFluentUI-FluentUIResources-ios */,
				DC8B7950F36091E881D0C0EB108A56D7 /* Pods-iOSProject */,
				6785AF3A16342D48B349E70A64F54E84 /* Pods-iOSProject-iOSProjectUITests */,
				716A169760385FB8F339D36EDAD9F6F3 /* Pods-iOSProjectTests */,
				95FF4EA8F555AE41A62D8405BB04FD5D /* SwipeActions */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		EDD72CA3DCA3423799442B1084C55EC6 /* Obscurable_ios */ = {
			isa = PBXGroup;
			children = (
				F2023AB9DE89186B164BB61E5FF6F424 /* BlurringView.swift */,
				49CFB64DADC6099E722D28E4E4DCCAB1 /* DimmingView.swift */,
				F104BE6C6A9888A94273C854EEDB9FE9 /* Obscurable.swift */,
			);
			name = Obscurable_ios;
			sourceTree = "<group>";
		};
		F0143A214510BF31EF934CEA5C087DAD /* Core_ios */ = {
			isa = PBXGroup;
			children = (
				FD76B04491BD820A0DA1E2B43CEDC6E2 /* AliasTokens.swift */,
				BA072894EA7E403A1E8157D86EE6C9CE /* ButtonDynamicColors.swift */,
				C54F33F7270ED5AE6F7A5B1BD8AE7878 /* Colors.swift */,
				9B5B8D09A6C133E60E88624CC65F2635 /* ControlHostingView.swift */,
				7A1C9C94BEC5608D7DE305E4180822A0 /* ControlTokenSet.swift */,
				DCF47F8FA0E92D20B30CBD4EE048ACD5 /* DynamicColor.swift */,
				B2631D831F5001B64C9BEDD6BCE13445 /* FluentTheme.swift */,
				7E78741BE32DB058AE93ADCCBA6948CA /* FluentUIFramework.swift */,
				2A28779FEA75D186EF896EF16A8ECC60 /* FluentUIHostingController.swift */,
				78777E17C95C21DFAF5FA16332036A41 /* FontInfo.swift */,
				DBFB4FCCACDFCE7FCF5EB367D3A30495 /* Fonts.swift */,
				EC2E73C06448CD60737C47EE44751459 /* GlobalTokens.swift */,
				E3B3E192E42DA21880DC62EA215DB7B3 /* LinearGradientInfo.swift */,
				3891CEEBB5A4B8FFB9E0FA827E01AAAA /* NSLayoutConstraint+Extensions.swift */,
				8258DFFB1E94835CC3E87FE47CC21530 /* ShadowInfo.swift */,
				738B9D5CFD1A8F6FB96730CB4C78AC83 /* String+Extension.swift */,
				A6579F74A035C0F50EA1A33EBEFDD622 /* SwiftUI+ViewAnimation.swift */,
				45BCD08F067AB985DD78CBCD4E749A2B /* SwiftUI+ViewModifiers.swift */,
				8EC41BB5A297CCB29ABF141D36B5D67A /* SwiftUI+ViewPresentation.swift */,
				10AC7C2113662A725FAA3E1389F6BA70 /* TokenizedControl.swift */,
				59B48080D1EFD51F65E3049A052FA1FB /* TokenizedControlView.swift */,
				50254538F3951872CDAFF851D017119D /* TokenSet.swift */,
				86A6D8888B34A734A9E122615D138F72 /* UIApplication+Extensions.swift */,
				60E48F5F2991D7AFAD34036D67BC593B /* UIColor+Extensions.swift */,
				4E8E6731BD3F7A9E606599ACFDAA1C5E /* UIImage+Extensions.swift */,
				F014B56A21A122D051BA050708238F7A /* UIKit+SwiftUI_interoperability.swift */,
				0A0C8E9132C915461271F846AEE7C0C4 /* UIScrollView+Extensions.swift */,
				B69EB277115DEA987339EBFBDBAF3B32 /* UIView+Extensions.swift */,
				10DD5AC62DB19BED2CA385217C290B25 /* Resources */,
			);
			name = Core_ios;
			sourceTree = "<group>";
		};
		FB8C2A703454FABD05E3132E2DA8609A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1FA1E5CC4365B53E5625126CE41A132B /* SwipeActions.modulemap */,
				D1AF8F21737D67ACC40F01E672C0416B /* SwipeActions-dummy.m */,
				B7BF04E1FB508C79CB3BF2DD42DFB902 /* SwipeActions-Info.plist */,
				C9D34FEC8866D7B0FFBB39A126689A95 /* SwipeActions-prefix.pch */,
				F281EDBAD41DA89812A4F0E3F6B08F28 /* SwipeActions-umbrella.h */,
				F48FA0C2B4AAA1C19F1278D784FEC1C0 /* SwipeActions.debug.xcconfig */,
				6E1BFAE51577B8C3CB22F6A9CE2C8F1B /* SwipeActions.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SwipeActions";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		3C7349987431B288809289FA11BF00B7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				820AC0982EA4A0C7894134EADB3C187C /* MicrosoftFluentUI-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4BB12B197CA47115A0B9FD0A5F28C89F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				BF1347C67C3F7458623EC0BA9357C70B /* SwipeActions-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6F3A29E0B06C6E2E4D7E06E9AB975C17 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				90AC57315952D84BE6AB5B3FF2952789 /* AzureCommunicationUICalling-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		81744D1E79447B531F2D4A605B9DE842 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				A248ABEE4E6B53EECB5F3BA42DBD28A7 /* Pods-iOSProject-iOSProjectUITests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		884D32B27ADA519AC832C5344E14E81C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				B72505F0447E22A038EF53907DD763D6 /* AzureCore-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CED08EC622A102670EE7851A0D19E866 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				4BA976F0289433E00AA78830CCB4D097 /* Pods-iOSProject-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F4FB835F2C20E08DBD23DCEB4E38B8E9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				3CE87FF273AE3865D5D04916EE674C9A /* Pods-iOSProjectTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9937CEFC18D101F52CB02002CAFBAF3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				BD34EC871BC5A38AC0329D3C5AF9AB37 /* AzureCommunicationCommon-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		07A6CC1D379046F22ADCE2752CDEF14A /* AzureCommunicationCommon */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F75C89AA415E8A3EE8B274720F139EB2 /* Build configuration list for PBXNativeTarget "AzureCommunicationCommon" */;
			buildPhases = (
				F9937CEFC18D101F52CB02002CAFBAF3 /* Headers */,
				E067D59FC065A276228CE6D975129BBC /* Sources */,
				A30EE9324981951053529CAB38F445C1 /* Frameworks */,
				9F4108133F8A7797A34D2DBD3C03BC6B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AzureCommunicationCommon;
			productName = AzureCommunicationCommon;
			productReference = 046BE0A5D32340465034A0DF75B74940 /* AzureCommunicationCommon */;
			productType = "com.apple.product-type.framework";
		};
		2349512128E8343A186BC45D4E7BDFAA /* MicrosoftFluentUI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3224E19C0C907D2EAEF4027E8BEB151B /* Build configuration list for PBXNativeTarget "MicrosoftFluentUI" */;
			buildPhases = (
				3C7349987431B288809289FA11BF00B7 /* Headers */,
				F57369CD0455714A467C65F09BB369B7 /* [CP-User] Optimize resource bundle */,
				610C21CEE3C1F63D18440DF66B51B265 /* Sources */,
				0BBA6388F8B6DC1044DAE9138E4ADB93 /* Frameworks */,
				0E75EFACBB7C74E4F564B56574841624 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2013EF3A48B05FFD19BEE9EF807C9F3F /* PBXTargetDependency */,
			);
			name = MicrosoftFluentUI;
			productName = FluentUI;
			productReference = 479B603E5C8EAD405D4DCA6344A9E171 /* MicrosoftFluentUI */;
			productType = "com.apple.product-type.framework";
		};
		25584D86D2424AB817DB16B37F1A9706 /* Pods-iOSProject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A5FB2CA797D46CD4DF64D86EEF6CD85 /* Build configuration list for PBXNativeTarget "Pods-iOSProject" */;
			buildPhases = (
				CED08EC622A102670EE7851A0D19E866 /* Headers */,
				E8A8B6E8A42C4E6F095B033AE4BD838A /* Sources */,
				D0A4ADAE8A03B21E43AD18C718DDBF70 /* Frameworks */,
				D7A45B10CA955D4267CA15174EFF0A51 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0BA1DC637AE5DD215F6BEF289D5FA4AF /* PBXTargetDependency */,
				3D00540CE760EC019041ADA93138DEA5 /* PBXTargetDependency */,
				8E59A2DB17074845653856D0CDE40FAD /* PBXTargetDependency */,
				A5EC45F2647CF4D8D50188E23C3231F1 /* PBXTargetDependency */,
				5AF376FC2AF2A1447E2097F6C8BFF860 /* PBXTargetDependency */,
				9F4104CA86D21279DA91366B7834F8E5 /* PBXTargetDependency */,
			);
			name = "Pods-iOSProject";
			productName = Pods_iOSProject;
			productReference = DC8B7950F36091E881D0C0EB108A56D7 /* Pods-iOSProject */;
			productType = "com.apple.product-type.framework";
		};
		6D50045A92B85D9D8A77085908200589 /* Pods-iOSProject-iOSProjectUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B16BFB70BFB46B09507AE81DDB23F83 /* Build configuration list for PBXNativeTarget "Pods-iOSProject-iOSProjectUITests" */;
			buildPhases = (
				81744D1E79447B531F2D4A605B9DE842 /* Headers */,
				CC19DF62BE6D37881A70FF77332C4D26 /* Sources */,
				BD795327359E6EDC648AB3D8D6542CC3 /* Frameworks */,
				CEC05C7C3C8175D9D257549D6CBED29C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DA541E47C78E908A20DFE3108BB0CA91 /* PBXTargetDependency */,
				F1BBCAECF452E26C4795D80D849668D3 /* PBXTargetDependency */,
				DF6C6606E3D5C4FB0FC41D9E94354CEF /* PBXTargetDependency */,
				39E7B594C11A1F31F3217E58B1928C29 /* PBXTargetDependency */,
				E35304D4B217067C789EF539555C0731 /* PBXTargetDependency */,
				91DA8A9E4ED0AC836F692D74A8162BB7 /* PBXTargetDependency */,
			);
			name = "Pods-iOSProject-iOSProjectUITests";
			productName = Pods_iOSProject_iOSProjectUITests;
			productReference = 6785AF3A16342D48B349E70A64F54E84 /* Pods-iOSProject-iOSProjectUITests */;
			productType = "com.apple.product-type.framework";
		};
		88C445CF76EB60DD5A2AFA17042F471A /* AzureCommunicationUICalling */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 54477A465530AC6D511F24067E44E2A0 /* Build configuration list for PBXNativeTarget "AzureCommunicationUICalling" */;
			buildPhases = (
				6F3A29E0B06C6E2E4D7E06E9AB975C17 /* Headers */,
				4CD5872D55C34716343E163742EEF94B /* Sources */,
				59F2608627736051701E2F97552BFBC3 /* Frameworks */,
				F3C873E5A6C943D700467E2CBCF54B8C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4EA342968B93FFBF8A7B18FE8E73FCDE /* PBXTargetDependency */,
				B0333BA08A50CA5ED0D25AEE017E5335 /* PBXTargetDependency */,
				D1B4F9CEC16F36F1C9FDD3F916A33D34 /* PBXTargetDependency */,
			);
			name = AzureCommunicationUICalling;
			productName = AzureCommunicationUICalling;
			productReference = C9C0E58C836CEECECBBA614EEAE53DC2 /* AzureCommunicationUICalling */;
			productType = "com.apple.product-type.framework";
		};
		B2C551DE8A6F4F26DF0F188E3BD28D81 /* MicrosoftFluentUI-FluentUIResources-ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC5EBC5D0CF4A28A849A7ADC41504368 /* Build configuration list for PBXNativeTarget "MicrosoftFluentUI-FluentUIResources-ios" */;
			buildPhases = (
				03343FEAAE80E15918464E9284CA6DFF /* Sources */,
				5F035379608155BAE1CA970519960403 /* Frameworks */,
				7E9B7E113E50E8F7B8F063193FD7D508 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MicrosoftFluentUI-FluentUIResources-ios";
			productName = "FluentUIResources-ios";
			productReference = 419FF83B5DE08258908F2BF54EA6B771 /* MicrosoftFluentUI-FluentUIResources-ios */;
			productType = "com.apple.product-type.bundle";
		};
		CDF34E949B35844DA11DAD701D3ACA7E /* SwipeActions */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D2C9B29A5AF21D5F75E9102B1B003431 /* Build configuration list for PBXNativeTarget "SwipeActions" */;
			buildPhases = (
				4BB12B197CA47115A0B9FD0A5F28C89F /* Headers */,
				81E23185C4ADFC1E33405F00BF22739C /* Sources */,
				6F8013D6932F3E3440BBB40E273A4094 /* Frameworks */,
				6B341953696B5322D4AEEE3C20CC1723 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwipeActions;
			productName = SwipeActions;
			productReference = 95FF4EA8F555AE41A62D8405BB04FD5D /* SwipeActions */;
			productType = "com.apple.product-type.framework";
		};
		D0A9CDB890AD60F443865DACE414420E /* Pods-iOSProjectTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE889601112292FAEA6BDC5B8349F563 /* Build configuration list for PBXNativeTarget "Pods-iOSProjectTests" */;
			buildPhases = (
				F4FB835F2C20E08DBD23DCEB4E38B8E9 /* Headers */,
				2ECE9BB70A3126FE6F338675D3193CDF /* Sources */,
				7606FBAB240087AEA19C8114CD4671F1 /* Frameworks */,
				547BD02CBE5051B84B70A09084F1F192 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				72BB8C15CECEF990C1E8F9F8312F8088 /* PBXTargetDependency */,
			);
			name = "Pods-iOSProjectTests";
			productName = Pods_iOSProjectTests;
			productReference = 716A169760385FB8F339D36EDAD9F6F3 /* Pods-iOSProjectTests */;
			productType = "com.apple.product-type.framework";
		};
		EF3D57598B2F6B611B951D5BC18E691F /* AzureCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88AA9FD5EAA07074D33B6838B7D0FB07 /* Build configuration list for PBXNativeTarget "AzureCore" */;
			buildPhases = (
				884D32B27ADA519AC832C5344E14E81C /* Headers */,
				53FC6E2CF8025ECE38118919C92BEE92 /* Sources */,
				2BB057DA4ED8AE11CB9DE32E56BA8339 /* Frameworks */,
				5BE7D872D69CF1F2A48E9BB3C4CF7BA7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AzureCore;
			productName = AzureCore;
			productReference = 2A0569FC6542319D29A5F4C7D66F6F5C /* AzureCore */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				ar,
				"ar-SA",
				ca,
				cs,
				da,
				de,
				"de-DE",
				el,
				en,
				"en-GB",
				"en-US",
				es,
				"es-ES",
				"es-MX",
				fi,
				"fi-FI",
				fr,
				"fr-FR",
				he,
				"he-IL",
				hi,
				hr,
				hu,
				id,
				it,
				"it-IT",
				ja,
				"ja-JP",
				ko,
				"ko-KR",
				ms,
				nb,
				"nb-NO",
				nl,
				"nl-NL",
				pl,
				"pl-PL",
				pt,
				"pt-BR",
				"pt-PT",
				ro,
				ru,
				"ru-RU",
				sk,
				sv,
				"sv-SE",
				th,
				tr,
				"tr-TR",
				uk,
				vi,
				zh,
				"zh-Hans",
				"zh-Hans-CN",
				"zh-Hant",
				"zh-Hant-TW",
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = E5C4A4AD4A92BCF9ADBF2D4DB73CEE9D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17D2EB69FA839BA1C46085F1047783CB /* AzureCommunicationCalling */,
				07A6CC1D379046F22ADCE2752CDEF14A /* AzureCommunicationCommon */,
				88C445CF76EB60DD5A2AFA17042F471A /* AzureCommunicationUICalling */,
				EF3D57598B2F6B611B951D5BC18E691F /* AzureCore */,
				2349512128E8343A186BC45D4E7BDFAA /* MicrosoftFluentUI */,
				B2C551DE8A6F4F26DF0F188E3BD28D81 /* MicrosoftFluentUI-FluentUIResources-ios */,
				25584D86D2424AB817DB16B37F1A9706 /* Pods-iOSProject */,
				6D50045A92B85D9D8A77085908200589 /* Pods-iOSProject-iOSProjectUITests */,
				D0A9CDB890AD60F443865DACE414420E /* Pods-iOSProjectTests */,
				CDF34E949B35844DA11DAD701D3ACA7E /* SwipeActions */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0E75EFACBB7C74E4F564B56574841624 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				31772EA29D3158C3730C1F49A66352D3 /* MicrosoftFluentUI-FluentUIResources-ios in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		547BD02CBE5051B84B70A09084F1F192 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5BE7D872D69CF1F2A48E9BB3C4CF7BA7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B341953696B5322D4AEEE3C20CC1723 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7E9B7E113E50E8F7B8F063193FD7D508 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2858FECC7E6BC63E54F4CBEC268C30B1 /* FluentUI-apple.xcassets in Resources */,
				78A6DE8437CE93437B32AA7151B34E62 /* FluentUI-ios.xcassets in Resources */,
				F828E9F6307C16CF23A1AE33B103863C /* Localizable.strings in Resources */,
				644B57FACA69DB033834FDF726B9B512 /* Localizable.stringsdict in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F4108133F8A7797A34D2DBD3C03BC6B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CEC05C7C3C8175D9D257549D6CBED29C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7A45B10CA955D4267CA15174EFF0A51 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3C873E5A6C943D700467E2CBCF54B8C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8B7414A2FCC65292E51DD1C7AE176E61 /* Assets.xcassets in Resources */,
				E24434CCD310B0B81872F9D17404DE12 /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		4B6724BB9F2E6B8B69BA870D6D2EF397 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/AzureCommunicationCalling/AzureCommunicationCalling-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/AzureCommunicationCalling/AzureCommunicationCalling-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/AzureCommunicationCalling/AzureCommunicationCalling-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F57369CD0455714A467C65F09BB369B7 /* [CP-User] Optimize resource bundle */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			name = "[CP-User] Optimize resource bundle";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "REMOVE_UNUSED_RESOURCES_SCRIPT_PATH=${PODS_TARGET_SRCROOT}/scripts/removeUnusedResourcesFromAssets.swift\n\n# Executes only once per \"pod install\" (if the script file exists)\nif [ -f ${REMOVE_UNUSED_RESOURCES_SCRIPT_PATH} ]; then\n    echo \"=== Removing unused resources from FluentUI-ios.xcassets ===\"\n\n    XCODEBUILDPARAMS=\"-quiet \"\n\n    if [ \"${CONFIGURATION}\" = \"Debug\" ]; then\n        CONDITIONALCOMPILATIONFLAGS=\"-D VERBOSE_OUTPUT\"\n        XCODEBUILDPARAMS=\"\"\n    fi\n\n    xcrun --sdk macosx swift ${CONDITIONALCOMPILATIONFLAGS} ${REMOVE_UNUSED_RESOURCES_SCRIPT_PATH} ${LOCROOT}/MicrosoftFluentUI/ios/FluentUI/Resources/FluentUI-ios.xcassets ${LOCROOT}/MicrosoftFluentUI/ios\n\n    echo \"=== Rebuilding resource bundle target ===\"\n    xcodebuild ${XCODEBUILDPARAMS} DISABLE_MANUAL_TARGET_ORDER_BUILD_WARNING=1 -project ${PROJECT_FILE_PATH} -target \"MicrosoftFluentUI-FluentUIResources-ios\" -sdk ${PLATFORM_NAME} -configuration ${CONFIGURATION} ARCHS=\"${ARCHS}\" CONFIGURATION_BUILD_DIR=\"${CONFIGURATION_BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" BUILT_PRODUCTS_DIR=\"${BUILT_PRODUCTS_DIR}\" ${ACTION}\n\n    # Deletes the script to ensure it will not be needlessly executed more than once after each \"pod install\"\n    rm ${REMOVE_UNUSED_RESOURCES_SCRIPT_PATH}\n\nfi";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		03343FEAAE80E15918464E9284CA6DFF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2ECE9BB70A3126FE6F338675D3193CDF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B1AB5500848393708B03BD35765372DF /* Pods-iOSProjectTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CD5872D55C34716343E163742EEF94B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				997ED482F100D24BB3B670DF84463A47 /* AccessibilityIdentifier.swift in Sources */,
				B274ECB8A026F7CB80F033878A4B741F /* AccessibilityProvider.swift in Sources */,
				2A9899A66AC14AC9024E4189D3A0E261 /* AccessibilityProviderNotificationsObserver.swift in Sources */,
				61765A5E5690C18B0BD10ABF32A6CFD5 /* AccessibilityProviderProtocol.swift in Sources */,
				7048171C689DCF6162F5DC580B5E7DEC /* ACSCallEndReasonExtension.swift in Sources */,
				0B1CCB7A086A3840223C533CFA7E1DD6 /* ACSCallingStateExtension.swift in Sources */,
				871AE75220C8F916B9D30CBCB087F80B /* ACSCameraFacingExtension.swift in Sources */,
				64A0DDFBFEE93D7B864CF14B341D9ED7 /* ACSDiagnosticQualityExtension.swift in Sources */,
				8466507A09421C86962E2FFD74D77206 /* ACSParticipantStateExtension.swift in Sources */,
				C053D19B3B4FCBCEF284D1A46088CB00 /* Action.swift in Sources */,
				FA70CBBD583B9638EFB5ECD6B71D6D9C /* AppLifeCycleManager.swift in Sources */,
				0AD2FF727D9AFE18C187EC5D90CBB5DE /* AppLifeCycleState.swift in Sources */,
				A9D1085AD1E272583E696752EB3BC6BA /* AppPhaseKey.swift in Sources */,
				48C77443C2E3653B173F82EE78F943F8 /* AppState.swift in Sources */,
				0D13C3A9C668A4AD69DE87675EC4BAF0 /* AppStateReducer.swift in Sources */,
				96C3C74EC68BBADC6A12AE116C0D7716 /* ArrayExtension.swift in Sources */,
				A4C441CB9DBFE64F801B244BA0A754A4 /* AudioDeviceListView.swift in Sources */,
				9CB2D264857B9DC90A3668A45B0541CD /* AudioDevicesListViewModel.swift in Sources */,
				748C8C2FB05B5EBAA3CF040E5533A71C /* AudioDeviceType.swift in Sources */,
				D04BCBA2E2B5B14554BF25306CC46FBE /* AudioSessionAction.swift in Sources */,
				5D99C7C3C274515349C0D1F4039BB74C /* AudioSessionManager.swift in Sources */,
				6F6D39DF02ECF2EEEC77FFD1E43AFA1A /* AudioSessionReducer.swift in Sources */,
				7231D1F159C9FD9E82F4351CBA93380D /* AudioSessionState.swift in Sources */,
				7BE4912307486F98DBE46AC05BE011B5 /* AvatarViewManager.swift in Sources */,
				067AEEC893BBB76B28A40822992906F5 /* AzureCommunicationUICalling-dummy.m in Sources */,
				9EC68CA90BA2C992B9140A505404EE82 /* BannerInfoType.swift in Sources */,
				870C6688B04EE42EDEA519B8F5FF1486 /* BannerTextView.swift in Sources */,
				FFC9550149952E8E76182293A4ADF6F3 /* BannerTextViewModel.swift in Sources */,
				C94F2AF5AC1953AC21D71AD91D770225 /* BannerView.swift in Sources */,
				A549BC2BF6AB6A977AFDC6C4D7A18A95 /* BannerViewModel.swift in Sources */,
				94A2A9D819FB7D0FADE118DF2000E390 /* BaseLocalizationProvider.swift in Sources */,
				893C1CE5FA620D47BA3899BCD597C8F1 /* BottomDrawer.swift in Sources */,
				F3A26AF2429D78D470A1F1125388D881 /* BottomToastView.swift in Sources */,
				AEE6C966645BB978343FB78C56FECBA6 /* BottomToastViewModel.swift in Sources */,
				7860B2E8C020DED4AC7F36619BC6747E /* ButtonState.swift in Sources */,
				D90077B85883DB66BDBA0019AC1A10E6 /* ButtonViewData.swift in Sources */,
				3EE4DD341FBDDEC908FABD169FDEBD9B /* ButtonViewDataAction.swift in Sources */,
				DDCADEDF9579952FD5F1624C10B92F5A /* ButtonViewDataReducer.swift in Sources */,
				C8D622C8DE9FE69D2E964874271E346E /* ButtonViewDataState.swift in Sources */,
				4A9B9408FAC3205F764A49BBC54B51E6 /* CallComposite.swift in Sources */,
				CB6A81C2F4DAB2B9714244700CCDA3C0 /* CallCompositeAudioVideoMode.swift in Sources */,
				ABB7007613FC1621A177A89A05BEE5CE /* CallCompositeCallState.swift in Sources */,
				0A732E9997B69403EFCAD1FA51C5CAB7 /* CallCompositeCaptionsData.swift in Sources */,
				7D75DDF706E962944A06F243864D8EDC /* CallCompositeDismissed.swift in Sources */,
				49D429D5DCA89BDBB3D75C83D928DB5C /* CallCompositeError.swift in Sources */,
				1B56FA5933883FF4166964433101EBE1 /* CallCompositeInternalError.swift in Sources */,
				FD934C6BDF4CD9161B5FB83F391D3C6E /* CallCompositeOptions.swift in Sources */,
				572ADE6C6BFE0D50BF5623E987442433 /* CallCompositeUserReportedError.swift in Sources */,
				6E96FDF25F812237FDAADB43AFDB0876 /* CallConfiguration.swift in Sources */,
				F40E0E1D711EF6FF514A6A8B610B95CA /* CallDiagnosticsReducer.swift in Sources */,
				B5F90F91CFCEAA62A832B8AA77BC33F3 /* CallDiagnosticsState.swift in Sources */,
				5E79866EB517A383F5E4D1F56D2BF3F0 /* CallDiagnosticsViewModel.swift in Sources */,
				39B40F49FF5A216729AF58F0B7927CD2 /* CallDurationManager.swift in Sources */,
				E0237CEFA04C715C99AA292FE83977A0 /* Caller.swift in Sources */,
				4D7094ED0D7529834E916A5C9F369F1A /* CallError.swift in Sources */,
				6B1E0DBACB4279E74C0DF6AE3FACD05F /* CallHistoryRecord.swift in Sources */,
				560AC5C1C1F25333522663FB68292D0A /* CallHistoryRepository.swift in Sources */,
				CA901B803EEC54F844BEEAC297F9D587 /* CallHistoryService.swift in Sources */,
				183E28251C071CABE2E513570AD1A925 /* CallInfoModel.swift in Sources */,
				4BEF9E8AA54A04150DEC21FD071D3454 /* CallingAction.swift in Sources */,
				3B518F3883FEA0D80995F528F210316F /* CallingMiddleware.swift in Sources */,
				C5CD03C467A80A7A57CCC6ED94B6E49A /* CallingMiddlewareHandler.swift in Sources */,
				C2537DD836556E012D2093CCA87CFE94 /* CallingMiddlewareHandlerExtension.swift in Sources */,
				0B66BC11A8E5A7090EDE33E2E6B68EF6 /* CallingReducer.swift in Sources */,
				0E4EC21C8F5799022759D47670B0B3C3 /* CallingSDKEventsHandler.swift in Sources */,
				CC1F4FAF5CD4E8017C6E4F0CFC19F864 /* CallingSDKInitializer.swift in Sources */,
				7E6E073E2EAF09F87D2B0C5AAE1E2C04 /* CallingSDKWrapper.swift in Sources */,
				FDD377595A5FA4A1C5423B18C647D48B /* CallingSDKWrapperProtocol.swift in Sources */,
				A7CCA2881E3E022D7DCBE01BE97D57AC /* CallingService.swift in Sources */,
				00012CD2197E36340938B07EEF666D4D /* CallingState.swift in Sources */,
				D59689481D5E877B4A031805827509AE /* CallingView.swift in Sources */,
				CDE09F3D7FEBC6389A262AEF901FC9F4 /* CallingViewModel.swift in Sources */,
				AE1340C07972DE692D716057CACB70C6 /* CallKitOptions.swift in Sources */,
				286D7E546FD42753E04E83F843E6FD15 /* CallKitRemoteInfo.swift in Sources */,
				903E4F4DE6F59563410AD4A5A54C484C /* CalllCompositeRttData.swift in Sources */,
				77FA3F4E2429E5C84EE5CFB65DF05673 /* CallPipVideoViewController.swift in Sources */,
				1D80851A8E6E50AF65CBE60A0BFF1B59 /* CallScreenControlBarOptions.swift in Sources */,
				715514B1FFF38115DDAA21399F965F83 /* CallScreenHeaderViewData.swift in Sources */,
				8F4281C05CD83612D710B051CA108004 /* CallScreenInfoHeaderAction.swift in Sources */,
				DED3F53D2AF0CD0F864ED180DAF58184 /* CallScreenInfoHeaderReducer.swift in Sources */,
				4925C496FE4CFE65DBB0DB5ECE41E7C1 /* CallScreenInfoHeaderState.swift in Sources */,
				A07FA882113AEC86D2FF72943A935413 /* CallScreenOptions.swift in Sources */,
				FC081D9AEAF8DE8D3D3DEBAC196D3725 /* CallStateManager.swift in Sources */,
				D26B46820607F8583CB04B7A24C2E5A5 /* CancelBag.swift in Sources */,
				390CE79BE2A8E2DC9149B6255C5F05CE /* CapabilitiesChangedEvent.swift in Sources */,
				ADCCF166BB69C5E6A8B2D1FF464DD650 /* CapabilitiesChangedNotificationMode.swift in Sources */,
				B1B7537983144DA2E9CB8E2335F7A433 /* CapabilitiesChangedReason.swift in Sources */,
				35B5229C8B752858CF0F7AD01F409228 /* CapabilitiesManager.swift in Sources */,
				BE7786B367859900E8498453FD8FE3CA /* CapabilityResolutionReason.swift in Sources */,
				D2A9DF57CCB4018BCBE5BB12418EA33B /* CaptionOptions.swift in Sources */,
				D62A2BC22B9615439F3D50E1B7987454 /* CaptionsAction.swift in Sources */,
				DBBE52890B1247FB2BA899A21A61E877 /* CaptionsAndRttLandscapeView.swift in Sources */,
				E31477CF0AF725ED75257F5F371D219D /* CaptionsErrorView.swift in Sources */,
				07A7D49DAF1A09444CD31393AA8912B6 /* CaptionsErrorViewModel.swift in Sources */,
				F32CBE3529B7052DC39EE6A3CE9B8628 /* CaptionsLanguageListView.swift in Sources */,
				10B343AF588E274CEC00E8C2864BAFCB /* CaptionsLanguageListViewModel.swift in Sources */,
				3BDA3BC3B55B0AE7E660D70D48780859 /* CaptionsReducer.swift in Sources */,
				DB5658E018A48FFD562EDEF715A208C1 /* CaptionsRttDataManager.swift in Sources */,
				C115BCAB1CBD85462DD70B6ACFBAF26E /* CaptionsRttInfoCellView.swift in Sources */,
				041BB5E46C732DF4C216271F8FC669A8 /* CaptionsRttInfoView.swift in Sources */,
				BCFE29686FCBABE5953CDDED29F9815A /* CaptionsRttInfoViewModel.swift in Sources */,
				864F7C53F93A9EBCE4D674C9229F526A /* CaptionsRttListView.swift in Sources */,
				ADD2BAE872977999CD684C4CEE17E2A5 /* CaptionsRttListViewModel.swift in Sources */,
				B18BADC9C74A4B67C3917F037E111F7C /* CaptionsState.swift in Sources */,
				64BEC5E162038C63083E84EF369E2120 /* ColorExtension.swift in Sources */,
				282F5EE7D90BEF0B025F068DBB0A1098 /* ColorThemeProvider.swift in Sources */,
				6EC18A3FC89C994461F9D7529772E709 /* CommunicationTokenCredentialError.swift in Sources */,
				B63509A7BBE1DB4E5A2D83985A8D1045 /* CompositeAvatar.swift in Sources */,
				F7E5694A11A0C05AD3E18FBFBED44439 /* CompositeButton.swift in Sources */,
				F99A7B2B475F0D6A9E127FEDAA502F82 /* CompositeErrorManager.swift in Sources */,
				49E1E62AA577678C688DFE60CA4331A8 /* CompositeExitManager.swift in Sources */,
				F58840BC231A5E599253641B3BBAAB33 /* CompositeViewFactory.swift in Sources */,
				850EA74D562CB4D4178C04EEAE013741 /* CompositeViewModelFactory.swift in Sources */,
				C2998C04C6FC0A081EDECC7ECB579A7A /* CompositeViewModelFactoryProtocols.swift in Sources */,
				9270F9C2A6BB15AE540706D6C9ABD7F9 /* ContainerUIHostingController.swift in Sources */,
				1C99B416317923E81E3E610561122264 /* ContainerView.swift in Sources */,
				3380FBAF65E543FA2E5E7A4DF5AD26A3 /* ControlBarView.swift in Sources */,
				E62E384E921B31180E707E4BBAC7CB5C /* ControlBarViewModel.swift in Sources */,
				38F8179AF90370C251183A999E7D7609 /* CustomAlert.swift in Sources */,
				24AAFE493727F70A1E1006F9B3976087 /* CustomButtonViewData.swift in Sources */,
				F313E5A0927808456AB990F38FF09FC9 /* CustomTextField.swift in Sources */,
				E5FDA0931ABB44311D95562B899195ED /* DebugInfo.swift in Sources */,
				27FEEAAADEA518362EAD341AF6B784F2 /* DebugInfoManager.swift in Sources */,
				4D89B0D8BFBA730FD62EAFB1EA2A6B94 /* DebugInfoSharingActivityViewModel.swift in Sources */,
				45CF83A1205B7D85C0336FF053933427 /* DefaultUserState.swift in Sources */,
				08F254A7D274B005A2855485EE013861 /* DeviceExtension.swift in Sources */,
				77BB0350BFC3FBA24D1C0A9DDB65520B /* DiagnosticConfig.swift in Sources */,
				B9570BDE98AC0190E82423B18F399763 /* DiagnosticsAction.swift in Sources */,
				09850CAC348521846D7CA3009F2C504B /* DraggableLocalVideoView.swift in Sources */,
				83203FCB16FBD8EC388C802DD46A5A09 /* DrawerBodyTextView.swift in Sources */,
				24E3EE0FA0254B5D383A63C220AA8C1C /* DrawerGenericItemView.swift in Sources */,
				E53890775A6CF6797D0D505C81EED172 /* DrawerListView.swift in Sources */,
				8EC2CA04459E3A5F98EE63D324720BF8 /* DrawerParticipantView.swift in Sources */,
				BA9394F4A669EF0D73AD97711E88099C /* DrawerSelectableItemView.swift in Sources */,
				AF27E8976D275799E4B8D80D20D5E2A2 /* DrawerTitleView.swift in Sources */,
				28867EDBF3E70B3AD454C260AF0D079C /* DrawerViewControllerProtocol.swift in Sources */,
				3BE786D7A57152648E67CD0D74EFD016 /* DrawerViewModels.swift in Sources */,
				F7CCE57AD55A710FBDB59EE06FDA336E /* EnvironmentValuesExtension.swift in Sources */,
				7B04D407EDF5487967F996C17E3ABD6F /* ErrorInfoView.swift in Sources */,
				01BFFEDD13236D7BC8F6F600218D8813 /* ErrorInfoViewModel.swift in Sources */,
				F67230B12A8461BC61D2BC32FF48C6CD /* ErrorReducer.swift in Sources */,
				6E9136211D2700BDCA1B1B13E45D3524 /* ErrorState.swift in Sources */,
				BBD03BB34CF95B105414B96A3D613EC7 /* ExpandableDrawer.swift in Sources */,
				004CE727ED816CEE02AA58E4106C33D2 /* FontExtension.swift in Sources */,
				32BD7F84FAEB49CB7B7212F00A39FF48 /* Icon.swift in Sources */,
				532A84077BA89E66B64FA7DCC9CEE47A /* IconAndLabelConversion.swift in Sources */,
				261B901F2D412AEB750635C17464AD1A /* IconButton.swift in Sources */,
				4A60E019D322B6FF680B16AB2BEB9D10 /* IconButtonViewModel.swift in Sources */,
				576B94ABB1FDAA10B850BFA1C65F82C4 /* IconProvider.swift in Sources */,
				5721DD8ECDF5397303D9ECB10968E9E0 /* IconWithLabelButton.swift in Sources */,
				16503E84B9FDF9BB5BA82EF5FB2856FC /* IconWithLabelButtonViewModel.swift in Sources */,
				434D6AF19EB3F8304417BCDDEE967E21 /* IncomingCall.swift in Sources */,
				25EAE6DDAAB60EA189D93197AE909384 /* IncomingCallCancelled.swift in Sources */,
				9B81CBDD01789BAABFE0939CF4BB6BF7 /* IncomingCallError.swift in Sources */,
				4759AC8A4ECD58B50CD4962EE42E58B2 /* InfoHeaderView.swift in Sources */,
				9CBA79540EC03C5E6A3E0C0EAE137D63 /* InfoHeaderViewModel.swift in Sources */,
				277B118EB3AFE1F1D92DC9EDD31A6E35 /* JoiningCallActivityView.swift in Sources */,
				1E6505CA48D7054A30D571EB83217FEF /* JoiningCallActivityViewModel.swift in Sources */,
				A6AE0F6CA34966FFF1AFBBC8EAD6824E /* KeybaordResponder.swift in Sources */,
				BC1C657450BBEC012B823B2BF92A2BA6 /* LandscapeAwareKeyboardWatcher.swift in Sources */,
				F339AE2403ED039AC7AD049485609F75 /* LeaveCallConfirmationMode.swift in Sources */,
				DBD530FDB4BC9771689682CB06C367C7 /* LeaveCallConfirmationView.swift in Sources */,
				AA224EA2FA97D6C33EF324CC03DA7CEA /* LeaveCallConfirmationViewModel.swift in Sources */,
				10DBC56710C46D54C27592E765AECBD9 /* LifecycleAction.swift in Sources */,
				8E93A7936C744CB6EEEE8DA3390877E2 /* LifeCycleReducer.swift in Sources */,
				DC3523C06FFE0A7D9C4C20331F57873F /* LoadingOverlayView.swift in Sources */,
				B582A75D2DE93B48002277B179829343 /* LoadingOverlayViewModel.swift in Sources */,
				FCB1D72DE6F4AB5727747F7573C1FE7A /* LobbyErrorHeaderView.swift in Sources */,
				9A72AFF0F7E6840386BA4C0B4588DD63 /* LobbyErrorHeaderViewModel.swift in Sources */,
				BE967C74F26E171BB209CF1006750A34 /* LobbyOverlayView.swift in Sources */,
				F8C9CA2F6449712F3E62B89999B3872E /* LobbyOverlayViewModel.swift in Sources */,
				9F9386C3632E4B70432B601E7FEC4349 /* LobbyWaitingHeaderView.swift in Sources */,
				DF59291E0D8390426B3ECE5AE65BBC1E /* LobbyWaitingHeaderViewModel.swift in Sources */,
				C8C3C174017433A71570A347BC1C5979 /* LocalizationKey.swift in Sources */,
				D7CBC453D215E76201BEC71E500B67AF /* LocalizationOptions.swift in Sources */,
				6998867E4319B98E94388557B105AC1A /* LocalizationProvider.swift in Sources */,
				123E77C020BC8E8ACFD716BECF95C28E /* LocalOptions.swift in Sources */,
				FA7406CB965C98B9F9E4F78590B8C17D /* LocalUserAction.swift in Sources */,
				8AA93ECC78A5F7257F16581A130AB79C /* LocalUserReducer.swift in Sources */,
				C7C1737A9BB41F8B8778D84448091EF9 /* LocalUserState.swift in Sources */,
				5192768BF76D3ADB9348068020DA005B /* LocalVideoStreamExtension.swift in Sources */,
				4664F6574FDCB8680BF6339EB71B3AA0 /* LocalVideoView.swift in Sources */,
				521D2DF708EAB3095E244564D71EE4AE /* LocalVideoViewModel.swift in Sources */,
				CBF1480A9ADDB6235BA03B1241E3D05B /* LockPhoneOrientation.swift in Sources */,
				F83D92144FBA5A1CBD3298E8091DCAC3 /* Logger.swift in Sources */,
				5354F1537FBE7D5F007D01F63109AA8D /* MappedSequence.swift in Sources */,
				531FB05655363F9AD0889C66823CCD5D /* MessageBarDiagnosticView.swift in Sources */,
				B7F0C4A4B585F5490C3B767AC12A64EA /* MessageBarDiagnosticViewModel.swift in Sources */,
				1F786B1750D4FB32BA78A1BA1B101522 /* Middleware.swift in Sources */,
				68E7A1D137A749BD86EFD2A659E98DBF /* MoreCallOptionsListView.swift in Sources */,
				60E3A8A423034D2BD6C3B5A50B003447 /* MoreCallOptionsListViewModel.swift in Sources */,
				BF1BDF2EC27FCEB110407E89742B2690 /* NavigationReducer.swift in Sources */,
				076CA0918153BC13A046DCFD3D9365A7 /* NavigationRouter.swift in Sources */,
				923CACEB45F780CD4FB3937071D1D6D8 /* NavigationState.swift in Sources */,
				B6FA26E4C6BD8B9EDC4C27AD08A4414E /* NetworkManager.swift in Sources */,
				3C1B96E5466C7B538455E1BE278DF239 /* NotificationCenterName.swift in Sources */,
				9D16E7894844FA8CB8EE80A2B485D975 /* OnHoldOverlayViewModel.swift in Sources */,
				B8E46902C7A9A36380C8AE7E733DBE78 /* OrientationManager.swift in Sources */,
				7CA90BBDB76FDE44C868537BA2D59F44 /* OrientationOptions.swift in Sources */,
				A7331272FF99E14988990B264EC4D1EE /* OrientationProvider.swift in Sources */,
				E769F5CF5BD999C4977C510BC9A01033 /* OverlayViewModelProtocol.swift in Sources */,
				817F00F76241C3146DF279AD0C78545C /* ParticipantCapability.swift in Sources */,
				F03CA69C7137D5D2818C7AB17B992A64 /* ParticipantCapabilityType.swift in Sources */,
				2F823F1277078274E855F71620E159C3 /* ParticipantGridCellVideoView.swift in Sources */,
				099445C2F8FF77EF4BE5B100E5A6248A /* ParticipantGridCellView.swift in Sources */,
				11C91CD2849745D44025A98C81764FDE /* ParticipantGridCellViewModel.swift in Sources */,
				CEF00066B7168CE6385B253FAAEAC24E /* ParticipantGridLayoutView.swift in Sources */,
				E3AB93B567DD80819D5820DC0838BE26 /* ParticipantGridView.swift in Sources */,
				C24C6924671F1C4AC38E854A9C381F76 /* ParticipantGridViewModel.swift in Sources */,
				B5FE4E9400E78F3A8D622ADBB7535FA8 /* ParticipantInfoModel.swift in Sources */,
				7071AC92461EE2672658C5656CD16AFF /* ParticipantListVIew.swift in Sources */,
				A0EBDB80AD38735DF62DDA2CAD2D7BCA /* ParticipantMenuView.swift in Sources */,
				5E693F265BE6B958A6D3F174E5C73E7D /* ParticipantMenuViewModel.swift in Sources */,
				1D110AB30DB7D87E42D85EB1FF85FFCD /* ParticipantRoleEnum.swift in Sources */,
				FAA700E2B6FC2DEA93E2E94131C36026 /* ParticipantsListCellViewModel.swift in Sources */,
				AF33BF20BB9142E45C5F26A06A7D83CE /* ParticipantsListViewModel.swift in Sources */,
				22CC8B323CB73C6F5DFEA4C915A89844 /* PermissionAction.swift in Sources */,
				9A198E38343834AC3949C5BE2A6D3399 /* PermissionReducer.swift in Sources */,
				7C60E255BC3FE5BA2BD7FD4DE7B14E33 /* PermissionsManager.swift in Sources */,
				18D633BAFB9AD09787C8D0C2EC6C5EF6 /* PermissionState.swift in Sources */,
				9199D5F26576ED63DE1858063F8B6F14 /* PipManager.swift in Sources */,
				4F90F1D4138F494805BCBA72BAB79465 /* PipReducer.swift in Sources */,
				E9C548B81CAAF6D5AA5A0876D79094F0 /* PopupModalView.swift in Sources */,
				3A3B2D9563B174579E9AC6737A5BAE7D /* PreferenceKey.swift in Sources */,
				B32FEC40A9FC4ABF969BD9ED88AF2B89 /* PreviewAreaView.swift in Sources */,
				E7BBB593F263D3153C5E3448A4B5977C /* PreviewAreaViewModel.swift in Sources */,
				E65B2A3502FB1E1C0F3D099BF6C9281F /* PrimaryButton.swift in Sources */,
				9EBBC551A7B58DA46E2C916D28BC0F25 /* PrimaryButtonViewModel.swift in Sources */,
				1F9BF4E6FB5B62AE01ADE2244CB3B2A9 /* PushNotification.swift in Sources */,
				2790630686192C7117AA00EC7282A33A /* PushNotificationEventType.swift in Sources */,
				AECC65BC69F0A4BE2C295A7CA569570C /* Reducer.swift in Sources */,
				040F0525EF382FE5A713A14B5FB5D29F /* RemoteOptions.swift in Sources */,
				C3F613AE8D9E19767E3AF0EDA78DD6F2 /* RemoteParticipantExtension.swift in Sources */,
				E335C0D824EC0F2E5F3FC2C8B2796A9B /* RemoteParticipantsAction.swift in Sources */,
				DCC9075E1558AB5E39F8F9E179CAFC0A /* RemoteParticipantsEventsAdapter.swift in Sources */,
				00996F78C630F3DD14B79F5B2D873F41 /* RemoteParticipantsManager.swift in Sources */,
				29803D4B7E0A813A4DF722ED07C727B7 /* RemoteParticipantsReducer.swift in Sources */,
				EB3216117924B208DD7ACDCA4619E622 /* RemoteParticipantsState.swift in Sources */,
				A58FBF8711DBC9C23E548DE08904FE9A /* RttAction.swift in Sources */,
				24574998DDC0C8ABD9B2B4357C5E23B0 /* RttReducer.swift in Sources */,
				48D389E273B5EE62089E2A8D6DFF168E /* RttState.swift in Sources */,
				6D45808DBEE399B47D9672DE4E8F1912 /* SetParticipantViewDataError.swift in Sources */,
				875DB9C1D0A956341CD747BDABDB4F3D /* SetupControlBarView.swift in Sources */,
				0E9A2C6304CD48CC78854CA1A6FF9B8A /* SetupControlBarViewModel.swift in Sources */,
				B74AFCF43E12BB1900F76A6F63778896 /* SetupScreenOptions.swift in Sources */,
				D3900ED96AAFAC207D6853EEB4AF3A11 /* SetupView.swift in Sources */,
				1E6DD61D41F5D325E140B4977D6AE1DC /* SetupViewModel.swift in Sources */,
				A0CB06E99DF2728EBC8B4135AB67F151 /* SharingActivityContainerController.swift in Sources */,
				20551876F01A8BE2F428DD77CF4C235F /* SharingActivityView.swift in Sources */,
				240E69B9C94317361259CD45DFB76A3C /* SourceViewSpace.swift in Sources */,
				E5864F334CF5348981D15460F32BB9F2 /* Store.swift in Sources */,
				CD84297515BFAB6E6CBCD76C684DCB85 /* StoreExtensions.swift in Sources */,
				FB212438A41F7D50A45573F9B101F84D /* StringConstants.swift in Sources */,
				4B5A832591B0DB70CA4250DAB720EA88 /* StyleProvider.swift in Sources */,
				B2B62275E2924073ABDFB6EDEB17835F /* SupportedLocale.swift in Sources */,
				E18C6A001DFC6927A44D4D8882E8C1FE /* SupportFormView.swift in Sources */,
				9EAED3DCCBE05B3F6C261D7A519EB440 /* SupportFormViewModel.swift in Sources */,
				64DF86492B045AF89441EEFF6EED5456 /* ThemeColor.swift in Sources */,
				3E5039BD48BF93C5ADCE6FC020494974 /* ThemeOptions.swift in Sources */,
				DB7457EF185A0AC8C3CB05A671781E81 /* ThrottleMiddleware.swift in Sources */,
				37FB31BBE45C81B56D802BFC09368B3C /* ToastNotificationAction.swift in Sources */,
				402A9A306612F515058D33C3F12B3BB0 /* ToastNotificationReducer.swift in Sources */,
				ABEBBAE1D9FCB525B59A9F9B44095FD2 /* ToastNotificationState.swift in Sources */,
				07CE389708067C49A4755F8736B79DFC /* UIViewControllerExtension.swift in Sources */,
				DC3C9797AE6878254A17EEAE1275418D /* UIWindowExtension.swift in Sources */,
				97ED16B71E09C0D849654C066D59E772 /* UpdatableOptionsManager.swift in Sources */,
				667B68B7D87EE6AE155209E6ED3E291D /* UserFacingDiagnosticModel.swift in Sources */,
				72B48EC500962A87FB39F0579998E2A7 /* VideoRenderView.swift in Sources */,
				7865AA9B34316F69BCC1F993EA3B3F68 /* VideoStreamInfoModel.swift in Sources */,
				A0E8362CF9CDB6E59F2B70C49DBA1C1D /* VideoViewManager.swift in Sources */,
				3F701639A93AB84D96849AE385360BD6 /* ViewExtension.swift in Sources */,
				D2E0F69D94FA97C46C622FDA13235665 /* ViewModifer.swift in Sources */,
				0BEC1974A593B70A5BEC52B86C2793D0 /* VisibilityAction.swift in Sources */,
				5C29138CE6075F159DC9383AEB70D30D /* VisibilityState.swift in Sources */,
				7F69E1DF8A4DE77F63F51958FB795F69 /* ZoomableVideoRenderView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		53FC6E2CF8025ECE38118919C92BEE92 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				722873FE4AF3915427CE6495CBD82756 /* AddDatePolicy.swift in Sources */,
				41291732F94EAD1160C25AB02D3380B3 /* ApplicationUtil.swift in Sources */,
				AD9EAD878DBA4B32B1E7A8A20B647374 /* AuthenticationPolicy.swift in Sources */,
				86B26E75590E1AECFB889D1561DF7E5E /* AzureCodable.swift in Sources */,
				112F36430BF331F07FA45D925AF534EA /* AzureCore-dummy.m in Sources */,
				DFE4576440237C3260F24EAAF8B63B37 /* AzureDate.swift in Sources */,
				7B9CA1CA57F05AD62126C88CF7A34EC0 /* AzureTask.swift in Sources */,
				4C7AB95FA66D689618108EA18A1B47C4 /* BundleInfoProvider.swift in Sources */,
				BF1B7DD52AEA2D205117621B04AE3D83 /* CancellationToken.swift in Sources */,
				308CF2BD47568B9EFDB000F02DD6D9AD /* ClientLogger.swift in Sources */,
				0A6A4EA5FBE8688A888B65481113EE1B /* Collections.swift in Sources */,
				E6A0081EF2354448852D843464B3ACCA /* ContentDecodePolicy.swift in Sources */,
				380FF7B3BDA4BAAD3A21945BA36F35DB /* ConvertingInitializers.swift in Sources */,
				69AAD3EAE6F49B2F1649AE851C6AA781 /* Copyable.swift in Sources */,
				A618C1435B5E5370C4E40DE5552B6619 /* CryptoUtil.swift in Sources */,
				EAA7AD200A405013C5494F4DF982F739 /* DataStringConvertible.swift in Sources */,
				05780F596DAC4D3D70BA4F462DCA9534 /* DeviceProviders.swift in Sources */,
				6DE2B00D0EAC8EF34139F9ED41A27B71 /* Errors.swift in Sources */,
				CF1166ACFFA80417403AC070919530D3 /* HeadersPolicy.swift in Sources */,
				A3F18C3D5A9A771AD0CEFE9768FD2572 /* HeadersValidationPolicy.swift in Sources */,
				DA290F587FD6B2242B1BB7365EFB0778 /* HTTPHeader.swift in Sources */,
				8F29F5649375CCB5181DB0CF1426D6D6 /* HTTPMethod.swift in Sources */,
				550C27E668AC6B100264F97CA79B97DC /* HTTPRequest.swift in Sources */,
				0DB0468B9EB0B3151B19E70818C724CE /* HTTPResponse.swift in Sources */,
				980BDCEB1715A31D6DC52226EFDEE2B4 /* KeychainUtil.swift in Sources */,
				ADB705E0492E7B7E1746BB257EBCA958 /* LocaleInfoProvider.swift in Sources */,
				CBD3525247E7204DC2376341381FCE4D /* LoggingPolicy.swift in Sources */,
				2567496D306EB1ED2897F7D9AF5F6828 /* NormalizeETagPolicy.swift in Sources */,
				C5D72626F9080298EB6992E110DC127C /* Pipeline.swift in Sources */,
				B57ABF7A78579F12BDA1376DE1C34E94 /* PipelineClient.swift in Sources */,
				E4F17D70EF97A7D68B1B1BD148E2214B /* PipelineContext.swift in Sources */,
				A029776B1B125654585699426ED2B04E /* PipelineRequest.swift in Sources */,
				FB97DF998AE56E03A8B37F80C59F2C22 /* PipelineResponse.swift in Sources */,
				903596F6C657E89C102EB6F51472DE1F /* PipelineStage.swift in Sources */,
				18AAA34A116A93EAAAB05AAA1EF94DE5 /* PlatformInfoProvider.swift in Sources */,
				694BCC1560EAE6810488B98087C4EDB7 /* ReachabilityManager.swift in Sources */,
				04C3F3AA550F744E5749E064B861CE86 /* ReachabilityManagerType.swift in Sources */,
				5CC6CA3E0FF429B58CE4126C64EA5F5F /* RegexUtil.swift in Sources */,
				9633EF16024F08F7177EBD4A516B84B5 /* RequestIdPolicy.swift in Sources */,
				8D50723CB05FE952BF458618F640469C /* RequestParameters.swift in Sources */,
				896FD6EEE6CD1F14D3205421317B8DD2 /* RequestString.swift in Sources */,
				9E1B57A3E22566C31B66B2996D25E0A3 /* RetryPolicy.swift in Sources */,
				219AEF40B7FB8E57A57160502455E945 /* StringUtil.swift in Sources */,
				5CB783C4A87A7A86EC8C3B15E1E66EB4 /* TelemetryOptions.swift in Sources */,
				EDEBB0F15FC4D7192DB24AC0D9405205 /* TransportOptions.swift in Sources */,
				F24750BD78018EFE82012D06945CEBAD /* TransportStage.swift in Sources */,
				65B980A2D63FB90664EC77873C7014FB /* URLHTTPResponse.swift in Sources */,
				A363187B7B1A09BDEC09B045A6417BA3 /* URLSessionTransport.swift in Sources */,
				AA5514F8128359D2B063AC71D0EEFA31 /* URLUtil.swift in Sources */,
				3F0318C7D7A0CFD151F4D6D8FE99F518 /* UserAgentPolicy.swift in Sources */,
				40CAEC14F66158653A0F4462D95E597B /* XMLMap.swift in Sources */,
				4E3D31E6D7749E2BAFBA13D2A4D81195 /* XMLModel.swift in Sources */,
				B3FFBA782629235B4E967816D48139EE /* XMLTree.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		610C21CEE3C1F63D18440DF66B51B265 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				1D030B97AE71B6D3D8A3CD0B4B8BF4C6 /* ActivityIndicator.swift in Sources */,
				C6A350BEBCC1DDDBC2F8E0BCD1748270 /* ActivityIndicatorModifiers.swift in Sources */,
				960793915B922E6F9352AA6E96091B3A /* ActivityIndicatorTokenSet.swift in Sources */,
				A03D90EA417F6174729E4F8196E36A77 /* AliasTokens.swift in Sources */,
				44B5FA5A42FCFBF6331CED0A5EE79D11 /* Avatar.swift in Sources */,
				C9A41DF7E78E69E7B9D255E3AF99CA35 /* AvatarGroup.swift in Sources */,
				D35BA384F0FE78932D2E93DD1C62BE94 /* AvatarGroupModifiers.swift in Sources */,
				7C70C4641AFC1991F3087C60ED1B645E /* AvatarGroupTokenSet.swift in Sources */,
				F5B26AD3442784E31DD9FBB873D40391 /* AvatarModifiers.swift in Sources */,
				0AEAB5AFFE8E2B4747C64CC205B6D952 /* AvatarTokenSet.swift in Sources */,
				51A19204A204FAC3B95FB2D91E216209 /* BadgeLabel.swift in Sources */,
				0B9CF34B3EF3EF41420F01D0FC7B2EE6 /* BlurringView.swift in Sources */,
				FCCED618656E5F6F019784872D36E1BD /* BottomSheetController.swift in Sources */,
				791B02C083780D36A357DEB07DF117D8 /* BottomSheetPassthroughView.swift in Sources */,
				C2C1DC83D9912679081E8437EF0DEF26 /* Button.swift in Sources */,
				B31140F4CD63C0184B540D0FDD0FE732 /* ButtonDynamicColors.swift in Sources */,
				2750BEC57D0D4BA8F5F9C21952E2D594 /* ButtonTokenSet.swift in Sources */,
				538465E79FFAB7B7451AF822B06DC4C6 /* CALayer+Extensions.swift in Sources */,
				23C0EC078926329B57D985B49295B46D /* CircleCutout.swift in Sources */,
				9712F1478FCEF0B1829324D5C275FB7B /* Colors.swift in Sources */,
				7172A6F1FE804A3BE80A899EA1BEDA4E /* ContentHeightResolutionContext.swift in Sources */,
				59A07A5D81053E47D66A75BFF6314B70 /* ControlHostingView.swift in Sources */,
				B76B645BF59DBA8F8744DFE59CE93E56 /* ControlTokenSet.swift in Sources */,
				F519B3BD976B0356616CDD5E4B7E364B /* DimmingView.swift in Sources */,
				78DE72917B38EECC0B1B34A75BDCA52C /* DrawerController.swift in Sources */,
				DCE2536547642376FDF6E13C8BB8F04F /* DrawerPresentationController.swift in Sources */,
				A6D8E9DCD84B60C464B4DF35BBD11088 /* DrawerShadowView.swift in Sources */,
				DF5F955B17E47ACFA9D51207755FAE0F /* DrawerTransitionAnimator.swift in Sources */,
				8680E20F4148F094991CE349681C2404 /* DynamicColor.swift in Sources */,
				6A9F0B4EF6ACED79B6F134AA5FA2721B /* FluentTheme.swift in Sources */,
				D44F81D792433346FE5965360979E39F /* FluentUIFramework.swift in Sources */,
				092C86B4BC6EBA14881BF268452B1F18 /* FluentUIHostingController.swift in Sources */,
				F1475EE661F4E3AB8AE5D284D3C31A2A /* FontInfo.swift in Sources */,
				35B894D33F0A632AE1164B90BD06EAA8 /* Fonts.swift in Sources */,
				74979E999F1418843279D7A22085CC47 /* GlobalTokens.swift in Sources */,
				58E5CAE150D818EAD4828D44F29D6440 /* Label.swift in Sources */,
				945B5AD0A88EC7C69C2DA282482192DB /* LinearGradientInfo.swift in Sources */,
				761508424E7CA85A4E6F5DA57B553284 /* MicrosoftFluentUI-dummy.m in Sources */,
				31DCC2BA6330AEE0C9DF122473076D28 /* MSFActivityIndicator.swift in Sources */,
				05925019960A2540DB24837EDA6F7C34 /* MSFAvatar.swift in Sources */,
				40E234CF438CE3B2063F8A25C021B4CB /* MSFAvatarGroup.swift in Sources */,
				C845937161B08CFBC1654BA55D39B920 /* MSFAvatarPresence.swift in Sources */,
				1EE71CFAA4B5619E7FFCE6C46C8B7EE3 /* NSLayoutConstraint+Extensions.swift in Sources */,
				8E427B7BB06FB2CE07495196E689AC5B /* Obscurable.swift in Sources */,
				307390689B73FF9E9242906D3B122B4F /* Persona.swift in Sources */,
				25CF662835E7D3F6B0D9E63BD9CE56A0 /* PopupMenuController.swift in Sources */,
				781ABE65B78D9C1AD34ECC64350FA98C /* PopupMenuItem.swift in Sources */,
				81334C1814E70D292CF29CD356B2D558 /* PopupMenuItemCell.swift in Sources */,
				DF56F1F23E31237E43B1FBA392BD4A14 /* PopupMenuProtocols.swift in Sources */,
				A0C312E4629F955F4F0ECF96846DE6D8 /* PopupMenuSection.swift in Sources */,
				6B82D93A6CC3ECEFCC23DBFDF1157F1A /* PopupMenuSectionHeaderView.swift in Sources */,
				F0209313CA71FAF9B05B8AC0F51F800D /* ResizingHandleView.swift in Sources */,
				FF6FE58370C1804E51F9BACEE95361A8 /* Separator.swift in Sources */,
				31A77B5C55CF55A10AB0099541C437D3 /* ShadowInfo.swift in Sources */,
				1A3BA465AF7F75D84AAFE5C4DEEF2092 /* String+Extension.swift in Sources */,
				7EB43A5B9CAA6FAC1F548165E7AF4284 /* SwiftUI+ViewAnimation.swift in Sources */,
				92AD5C91B0836AFF0821AD0147DD8FE6 /* SwiftUI+ViewModifiers.swift in Sources */,
				29056FA748ED2B508C524B1EDCF38B68 /* SwiftUI+ViewPresentation.swift in Sources */,
				8ACD6FFF4F04ECB0A55AEA6767C37787 /* TableViewCell.swift in Sources */,
				57564CFE260F7C36696385F1592D8E56 /* TableViewCellTokenSet.swift in Sources */,
				A9438F3D65F0DF9B3E8C2D61DFA1B0F9 /* TableViewHeaderFooterView.swift in Sources */,
				7CC5F8AAEA1EBE60E8797C0F41F02397 /* TokenizedControl.swift in Sources */,
				15D69772913E1C8DE4A9B0627154FC38 /* TokenizedControlView.swift in Sources */,
				19B507AC3EAB7002E57C7CCEA32AB907 /* TokenSet.swift in Sources */,
				151DF570DE4E6AB1BE2DE55DFC9348DC /* TouchForwardingView.swift in Sources */,
				29C93E51E27245645CB5E92BC468BD32 /* UIApplication+Extensions.swift in Sources */,
				2761FE4D12BE1148B6D6EDE368275629 /* UIColor+Extensions.swift in Sources */,
				B00044F93F8D235CE7359A3287DB5AB5 /* UIImage+Extensions.swift in Sources */,
				CEBBA78362F881199FFAA41410D9C489 /* UIKit+SwiftUI_interoperability.swift in Sources */,
				C60D53B80B2990363A5263716969D2C7 /* UIScrollView+Extensions.swift in Sources */,
				70497CF99ECA33D90F1EBDFF12261286 /* UIView+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		81E23185C4ADFC1E33405F00BF22739C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				048C6A7D070384B9D1792A505C6DC75F /* MeasureSizeModifier.swift in Sources */,
				DACF550D77C9CA4F9C1E5EE16C031CBE /* SwipeActionModifier.swift in Sources */,
				0B5D4A5323010339A3469A65567F028F /* SwipeActions.swift in Sources */,
				3C9D912227D956E95EFE31594E486D70 /* SwipeActions-dummy.m in Sources */,
				980F484758900A8E26DFCDF08F9B293B /* ValueChangedModifier.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CC19DF62BE6D37881A70FF77332C4D26 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				06B1B19313A0BF97FF2032EFC199226C /* Pods-iOSProject-iOSProjectUITests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E067D59FC065A276228CE6D975129BBC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9A580B25FA81C4C3E166C3F37E9D4E88 /* AutoRefreshTokenCredential.swift in Sources */,
				1802CF0C0C13533FB3BB8DEF72B99E88 /* AzureCommunicationCommon-dummy.m in Sources */,
				A8A5EFBD3F421934466BAEE0E3321AF7 /* CommunicationAccessToken.swift in Sources */,
				65B23BFC83E0B6338B8BCAD2BF7E17D7 /* CommunicationCloudEnvironment.swift in Sources */,
				95DA64658B1E4B7379634DF920403CA7 /* CommunicationTokenCredential.swift in Sources */,
				F79F0E2AEF260D8F9B31B33837D6C8A9 /* CommunicationTokenCredentialProviding.swift in Sources */,
				4949D2A7527FE086E1EF0045E4B4452E /* CommunicationTokenRefreshOptions.swift in Sources */,
				C27C8E871EF77FC740C447CE07303AD1 /* Identifiers.swift in Sources */,
				3CE0B54BC9C2BF95C3AF25C3B771B978 /* JwtTokenParser.swift in Sources */,
				004FF5F360B5F0E6B7B3FCD710759D69 /* StaticTokenCredential.swift in Sources */,
				2B10BEFAF62BCBDDBA6F52A8078A741E /* ThreadSafeRefreshableAccessTokenCache.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8A8B6E8A42C4E6F095B033AE4BD838A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7A3255B1EDBBC760E7723A7AD487E800 /* Pods-iOSProject-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0BA1DC637AE5DD215F6BEF289D5FA4AF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCalling;
			target = 17D2EB69FA839BA1C46085F1047783CB /* AzureCommunicationCalling */;
			targetProxy = 088C2E19F4D91AFA170C36D0763ED716 /* PBXContainerItemProxy */;
		};
		2013EF3A48B05FFD19BEE9EF807C9F3F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "MicrosoftFluentUI-FluentUIResources-ios";
			target = B2C551DE8A6F4F26DF0F188E3BD28D81 /* MicrosoftFluentUI-FluentUIResources-ios */;
			targetProxy = C8FC637B326613CEF78634A2D9143985 /* PBXContainerItemProxy */;
		};
		39E7B594C11A1F31F3217E58B1928C29 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCore;
			target = EF3D57598B2F6B611B951D5BC18E691F /* AzureCore */;
			targetProxy = 1BDAB1CD1EADD01298C90749CAD0E927 /* PBXContainerItemProxy */;
		};
		3D00540CE760EC019041ADA93138DEA5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCommon;
			target = 07A6CC1D379046F22ADCE2752CDEF14A /* AzureCommunicationCommon */;
			targetProxy = C55D9C08B93D17D62647C8215F0D664F /* PBXContainerItemProxy */;
		};
		4EA342968B93FFBF8A7B18FE8E73FCDE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCalling;
			target = 17D2EB69FA839BA1C46085F1047783CB /* AzureCommunicationCalling */;
			targetProxy = 9C7EBC2055043319D28376144E186886 /* PBXContainerItemProxy */;
		};
		5AF376FC2AF2A1447E2097F6C8BFF860 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MicrosoftFluentUI;
			target = 2349512128E8343A186BC45D4E7BDFAA /* MicrosoftFluentUI */;
			targetProxy = E76DF037B2BD3BD21442F3249536681E /* PBXContainerItemProxy */;
		};
		72BB8C15CECEF990C1E8F9F8312F8088 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-iOSProject";
			target = 25584D86D2424AB817DB16B37F1A9706 /* Pods-iOSProject */;
			targetProxy = 32C433BFE03771F3FA91F51AB289B6AB /* PBXContainerItemProxy */;
		};
		8E59A2DB17074845653856D0CDE40FAD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationUICalling;
			target = 88C445CF76EB60DD5A2AFA17042F471A /* AzureCommunicationUICalling */;
			targetProxy = 997DEF575962B371768D4C92D9887B66 /* PBXContainerItemProxy */;
		};
		91DA8A9E4ED0AC836F692D74A8162BB7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SwipeActions;
			target = CDF34E949B35844DA11DAD701D3ACA7E /* SwipeActions */;
			targetProxy = F1DF4CFC83009D6C6394AE006136A52E /* PBXContainerItemProxy */;
		};
		9F4104CA86D21279DA91366B7834F8E5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SwipeActions;
			target = CDF34E949B35844DA11DAD701D3ACA7E /* SwipeActions */;
			targetProxy = 0619FF6F97266E7118C5FA5941CC13F1 /* PBXContainerItemProxy */;
		};
		A5EC45F2647CF4D8D50188E23C3231F1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCore;
			target = EF3D57598B2F6B611B951D5BC18E691F /* AzureCore */;
			targetProxy = B15589004DFD598CFF545A003378DC1C /* PBXContainerItemProxy */;
		};
		A5F48A46EAE76721109F60E62097EE8E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCommon;
			target = 07A6CC1D379046F22ADCE2752CDEF14A /* AzureCommunicationCommon */;
			targetProxy = C98525B04DC5F826D967728B8A52689D /* PBXContainerItemProxy */;
		};
		B0333BA08A50CA5ED0D25AEE017E5335 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCore;
			target = EF3D57598B2F6B611B951D5BC18E691F /* AzureCore */;
			targetProxy = CB3C652FAEAE27ECC5E2487B094562E3 /* PBXContainerItemProxy */;
		};
		D1B4F9CEC16F36F1C9FDD3F916A33D34 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MicrosoftFluentUI;
			target = 2349512128E8343A186BC45D4E7BDFAA /* MicrosoftFluentUI */;
			targetProxy = 573E8727D7C2E8AEEE10D75BE6D0EC01 /* PBXContainerItemProxy */;
		};
		DA541E47C78E908A20DFE3108BB0CA91 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCalling;
			target = 17D2EB69FA839BA1C46085F1047783CB /* AzureCommunicationCalling */;
			targetProxy = E0FA750CC6ACF9117B834C7F20C7E079 /* PBXContainerItemProxy */;
		};
		DF6C6606E3D5C4FB0FC41D9E94354CEF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationUICalling;
			target = 88C445CF76EB60DD5A2AFA17042F471A /* AzureCommunicationUICalling */;
			targetProxy = B5875060BE78221DA93587B5CF1DB9C9 /* PBXContainerItemProxy */;
		};
		E35304D4B217067C789EF539555C0731 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MicrosoftFluentUI;
			target = 2349512128E8343A186BC45D4E7BDFAA /* MicrosoftFluentUI */;
			targetProxy = DC52956F2C78938B1D4E3AB46BB52EAA /* PBXContainerItemProxy */;
		};
		F1BBCAECF452E26C4795D80D849668D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AzureCommunicationCommon;
			target = 07A6CC1D379046F22ADCE2752CDEF14A /* AzureCommunicationCommon */;
			targetProxy = 1B5096EDE33C89885F49393410B3B251 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		3460057AB7FC55D43DAD719B427A91F6 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8BABD6164944B7C7CDCF83DC121E7C4E /* Localizable.strings */,
				13833A3605E4A28CC07DFDD2445F421C /* Localizable.strings */,
				483EB492B527B21DE34EA5A33C1C0719 /* Localizable.strings */,
				F089D9BFC3EB292E450C8632A4D6D979 /* Localizable.strings */,
				CD385314B765653786FE3902103782F8 /* Localizable.strings */,
				941D6002A365A05FA76FD7816D84CA06 /* Localizable.strings */,
				F634937F33B5FD66068E7702E56736E7 /* Localizable.strings */,
				D6481E98B7A38F2B8A5A633E8F041615 /* Localizable.strings */,
				1F9A5C642CB238E19627D5BBC76D9986 /* Localizable.strings */,
				D4CA5FAC044097ED5E5D38C6647E4EFF /* Localizable.strings */,
				CC8622DA90237565DB4F6D542F8B2352 /* Localizable.strings */,
				594E9A3763EF54330E716784A1E0D5F1 /* Localizable.strings */,
				3C443BA32FEECFFEA8748B4509FC245D /* Localizable.strings */,
				0CCDB10E011B8B69D78A59A413667628 /* Localizable.strings */,
				D424C6095677E67FDC7EC5033AC9B352 /* Localizable.strings */,
				886D76936C176D59BE4546B272852002 /* Localizable.strings */,
				07B9394E28B761F5D147D58A4B28D149 /* Localizable.strings */,
				42332C34304DD69946E9B7CAE443E868 /* Localizable.strings */,
				CCBB2A8C7D82827C636E1ED1C11C21D2 /* Localizable.strings */,
				A0EF3CD0E5F5E543E10684302F560D5D /* Localizable.strings */,
				9E304F4FC271D0C3BB4BAF71B19BE56A /* Localizable.strings */,
				CBC8D9E4D8904DC615DD604E62F32ADA /* Localizable.strings */,
				7EF3EB30D6621EE0834EAE4441AF795A /* Localizable.strings */,
				A46EF6701DCA54F35F4AFF6ADC7F4BF5 /* Localizable.strings */,
				1AA6690CD1D0A8BF3997FFA33BD173AD /* Localizable.strings */,
				791BF17049398C5A57139824CDDE8676 /* Localizable.strings */,
				DF3BF5CBC4BB9CA8283CC316BB8065E9 /* Localizable.strings */,
				0AAB6A9BF52DF5BCB57CDDA430A8192A /* Localizable.strings */,
				A3BD5FC2A4C8E478DD72642192A9EFEA /* Localizable.strings */,
				8FD7D688A01765E4FD6E4D9710EB9E15 /* Localizable.strings */,
				72BA9B572A2DAA9B61329A163AC62344 /* Localizable.strings */,
				E1E994C8CDAD72BE943ECE7BB6D2DDA8 /* Localizable.strings */,
				4D1CE550E0EA8ECCE19FD48113D7B9A3 /* Localizable.strings */,
				4279AAB381C0731002968A5C15E82F35 /* Localizable.strings */,
				4C61322F803159EF8E0006CA32C8D4ED /* Localizable.strings */,
				57FE731BE327AC64D43677BAA6B299CB /* Localizable.strings */,
			);
			name = Localizable.strings;
			path = ios/FluentUI/Resources/Localization;
			sourceTree = "<group>";
		};
		7708492A1C3815979A7398F14E2015BD /* Localizable.stringsdict */ = {
			isa = PBXVariantGroup;
			children = (
				FE44E69AEBE1F09BC91574F644148267 /* Localizable.stringsdict */,
				5863B19854CB996EEC6B3CE4C2F6EED6 /* Localizable.stringsdict */,
				D55565D4DD5BBC5ECD09D15C34DD344B /* Localizable.stringsdict */,
				8633FC83636AAFA95C3E5202191FC60A /* Localizable.stringsdict */,
				B8D4EBA729259D2F05338322DBF58230 /* Localizable.stringsdict */,
				6D6A1B022215268BD3A32D50A6BE28AE /* Localizable.stringsdict */,
				FC3C6B644945B153ED861D09D9C6ECE7 /* Localizable.stringsdict */,
				456791E520F7D5F02377D1C80E535E15 /* Localizable.stringsdict */,
				0C8A62FD073875D50F76D9A5A657A69C /* Localizable.stringsdict */,
				CF7E40D9F1651957F94C8F63B2441FFA /* Localizable.stringsdict */,
				36A403B0972A7E9C66FB5CADF435B3A3 /* Localizable.stringsdict */,
				F617E050123E74BFEF251A9548E361C8 /* Localizable.stringsdict */,
				C97B85DEC7A69CC0F7604C31FC0FFF69 /* Localizable.stringsdict */,
				742D945200D910034338D83A01229324 /* Localizable.stringsdict */,
				8D8EAC68E816B1C69E0AE28EE2C391C7 /* Localizable.stringsdict */,
				9F2B235E6DA4E7074956A11CE140F96C /* Localizable.stringsdict */,
				E3BDDBF7F0690434DE2F2B59F3399C49 /* Localizable.stringsdict */,
				900549E16FD6D5016483CD194733DE86 /* Localizable.stringsdict */,
				416555BCA7BF7A22F59E7E19BF44833F /* Localizable.stringsdict */,
				DCE7AE9F622C44DC0FD8C2FA90CCBD8F /* Localizable.stringsdict */,
				90D2121BD58D81957CE8795045BD56E5 /* Localizable.stringsdict */,
				80FC77A08DA4DF9314E25B83FB4EAC7F /* Localizable.stringsdict */,
				D161AA6B9F66CB78E041E11E46771F3D /* Localizable.stringsdict */,
				555C43BF893F4A3B9EB64C35B2A7CD9C /* Localizable.stringsdict */,
				28E527D402097C1D2F1D9EAAE0FA6F69 /* Localizable.stringsdict */,
				6AFA5583BAA14DCA4B53A4665A015A39 /* Localizable.stringsdict */,
				8AC9318696C94A812476415CD384D100 /* Localizable.stringsdict */,
				36A29891482E11A9ED17B985536ED272 /* Localizable.stringsdict */,
				F1FAB415AA27006FA2C4D6060C96D007 /* Localizable.stringsdict */,
				7F445959FCF588BB44F78248770A3B35 /* Localizable.stringsdict */,
				72937CDD4EA795451A56FA0099CACA01 /* Localizable.stringsdict */,
				56BAC2316328AEF2D1C004A275517C81 /* Localizable.stringsdict */,
				1011ECE09779CE830E443699EAB17ECF /* Localizable.stringsdict */,
				171C078557A25531933FDE37281D5857 /* Localizable.stringsdict */,
				E747587576023D9CE752536BA706103B /* Localizable.stringsdict */,
				DDA6CB85C0A67195A3930E0AA77D8E85 /* Localizable.stringsdict */,
			);
			name = Localizable.stringsdict;
			path = ios/FluentUI/Resources/Localization;
			sourceTree = "<group>";
		};
		94F313C6263B4527440AEF93A30811F9 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				A1D949BFA428515E6E5BE51E2BC56AA4 /* Localizable.strings */,
				93C650BBF867FEDC8E4467E54E7607AC /* Localizable.strings */,
				A10A8DF05447B6E1027958EF2A29CBB2 /* Localizable.strings */,
				1E53DC65DE8E9633668A2386576EC8CE /* Localizable.strings */,
				653F2725768132F7554E94A593C38EFA /* Localizable.strings */,
				8A207EDDB62D6F12026376F41C1695D4 /* Localizable.strings */,
				53A1A5BCE00C1B6C8FA9ADD27AC44D58 /* Localizable.strings */,
				95C2200421A2DC6122060706B419D7AE /* Localizable.strings */,
				DE7DC946E1FDE62288D210CA764AA945 /* Localizable.strings */,
				519C152346F424BA0E5F1C4BC22965D9 /* Localizable.strings */,
				57DD7FD9EFDC59139E768AC3F545C3F0 /* Localizable.strings */,
				3B8B2050F4151E9A649065F3EB9D530F /* Localizable.strings */,
				CC83D9FAB90DB69EF02A99982229F355 /* Localizable.strings */,
				4DBF4F4963BAAE6B33AC384CA7A7B51E /* Localizable.strings */,
				5F3ABC536DA58980997E4C4E10F27242 /* Localizable.strings */,
				D717ADE98D1F65B48398DE5C0111F92F /* Localizable.strings */,
				3F5B263FAB571222869EB06710AFB542 /* Localizable.strings */,
				11C0259A00F0E211FFD4EE6930AF97C2 /* Localizable.strings */,
				2AFC20F15296528F104AF39E5CEB1835 /* Localizable.strings */,
				CFA4F9FB35E3FF33F252B3E6E5F869E0 /* Localizable.strings */,
				2DCE6587E8613AB0EBAC85C0F2E21406 /* Localizable.strings */,
				EEB43FF77D3B83CB073D26F18012DA90 /* Localizable.strings */,
				227378F30354FFDF9EB8590194AC7ED6 /* Localizable.strings */,
				0393B1EE7DB441D6C35A80725513A901 /* Localizable.strings */,
				B4EB955FDA92A3A926D1071BF27D706C /* Localizable.strings */,
				39E8996DDD47D8ADC59F0D5EB78254C5 /* Localizable.strings */,
				AB8E7E3F0883631AB74A91703B7C662F /* Localizable.strings */,
				6EF0DE753E0DF42EFB0DE21A97313535 /* Localizable.strings */,
				E0ED952467261B17919AFE46AB64006F /* Localizable.strings */,
				6AD3746AC595789F2E09556DB5BC075E /* Localizable.strings */,
				F7324E9A62AE54E6C041797FE58F8B2C /* Localizable.strings */,
				8CACDB86BE30C1B24E0BCAD244C93DF0 /* Localizable.strings */,
				3019C36E560277B5F3C1E6700ECCA637 /* Localizable.strings */,
				8E89A1B2DEFEE30239E7A7CBE73CB400 /* Localizable.strings */,
				01E187A85DCE070B8998DAABB8D66120 /* Localizable.strings */,
				D1F9A566DC704B783820D0F2B94634A7 /* Localizable.strings */,
				96186C0BC9C5EC5F394EF114B22D0F9B /* Localizable.strings */,
				C9D33859F40D0B95059452BDCF71D7B4 /* Localizable.strings */,
				13455CE0A261CCF3D58003DD1E63736F /* Localizable.strings */,
				AD512F53FB63B4C2C4C108D1491B480C /* Localizable.strings */,
			);
			name = Localizable.strings;
			path = AzureCommunicationUI/sdk/AzureCommunicationUICalling/Sources/Localization;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0721EA7255B3D2311E81B9BD26DCB9FD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2DE0809D6E8F91E5D2CD6A5099D53334 /* Pods-iOSProjectTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProjectTests/Pods-iOSProjectTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProjectTests/Pods-iOSProjectTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1A773A30C590EE479DDBDBA7B1309648 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9CC109085972537E5F701565A5A2245A /* AzureCommunicationCommon.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon.modulemap";
				PRODUCT_MODULE_NAME = AzureCommunicationCommon;
				PRODUCT_NAME = AzureCommunicationCommon;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2F5B7EF26BCB25F3087B46A9247F4244 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 425529FC72F1D78C2518CD971694D55D /* AzureCommunicationUICalling.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling.modulemap";
				PRODUCT_MODULE_NAME = AzureCommunicationUICalling;
				PRODUCT_NAME = AzureCommunicationUICalling;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.8;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		32C30850DDF0EC701859ADB707425D43 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4942C9B87F243C0F84EF40A55FD95C73 /* AzureCommunicationCalling.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4E1D40535016BAA382485C412506FADA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B670DC714CFE9043492068C3CD54B8E /* MicrosoftFluentUI.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MicrosoftFluentUI";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				IBSC_MODULE = FluentUI;
				INFOPLIST_FILE = "Target Support Files/MicrosoftFluentUI/ResourceBundle-FluentUIResources-ios-MicrosoftFluentUI-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				PRODUCT_NAME = "FluentUIResources-ios";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		5E9A37BA873B5E49F3389DBEEBD6E8F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5E835ED93C3C8CFF613B0201B069C57E /* MicrosoftFluentUI.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MicrosoftFluentUI";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				IBSC_MODULE = FluentUI;
				INFOPLIST_FILE = "Target Support Files/MicrosoftFluentUI/ResourceBundle-FluentUIResources-ios-MicrosoftFluentUI-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				PRODUCT_NAME = "FluentUIResources-ios";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		70564407E7BDDDC9B6D6523F93778966 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E9FD78EC0FD1B3A8E86D82BFE118A7C3 /* AzureCore.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCore/AzureCore-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCore/AzureCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCore/AzureCore.modulemap";
				PRODUCT_MODULE_NAME = AzureCore;
				PRODUCT_NAME = AzureCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		8A076EC0978652AEC2A59B9E996EB3EE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5E835ED93C3C8CFF613B0201B069C57E /* MicrosoftFluentUI.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI.modulemap";
				PRODUCT_MODULE_NAME = FluentUI;
				PRODUCT_NAME = FluentUI;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.7;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		91AB63A45CB89E733B62FBCCD40D1F2F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9275DCC7B88C1F257EFB7A5BC59AFFF2 /* Pods-iOSProject.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProject/Pods-iOSProject-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProject/Pods-iOSProject.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		929DAE3EE6669F1ADEF6C75694227F37 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 13935B8E8FEF162E9E7A0E9846F97225 /* AzureCore.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCore/AzureCore-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCore/AzureCore-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCore/AzureCore.modulemap";
				PRODUCT_MODULE_NAME = AzureCore;
				PRODUCT_NAME = AzureCore;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		95B3979CE997EEF413178F101A0D63C1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 240C73B422C7877840DAEBD4742F091D /* AzureCommunicationCalling.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		99C78A38711FA735E37D8FBF6EE0B0C9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C211D9644098BE447DD459B8CD561AC4 /* Pods-iOSProject.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProject/Pods-iOSProject-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProject/Pods-iOSProject.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		AA8FA0A06CEF1C3C2EDCD49C891BBB9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6E1BFAE51577B8C3CB22F6A9CE2C8F1B /* SwipeActions.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SwipeActions/SwipeActions-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SwipeActions/SwipeActions-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SwipeActions/SwipeActions.modulemap";
				PRODUCT_MODULE_NAME = SwipeActions;
				PRODUCT_NAME = SwipeActions;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C2EAB1D67416083CB17650910787397B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F48FA0C2B4AAA1C19F1278D784FEC1C0 /* SwipeActions.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SwipeActions/SwipeActions-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SwipeActions/SwipeActions-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SwipeActions/SwipeActions.modulemap";
				PRODUCT_MODULE_NAME = SwipeActions;
				PRODUCT_NAME = SwipeActions;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C818A130C776806D0AC80D65691D5C17 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB9FDF128C332399D1C6B509AFA9ABD2 /* Pods-iOSProject-iOSProjectUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProject-iOSProjectUITests/Pods-iOSProject-iOSProjectUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProject-iOSProjectUITests/Pods-iOSProject-iOSProjectUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E1B9D41B97CFD21A71517E7202E8445D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 238398EADAF89E6189D854EB9582F9B8 /* AzureCommunicationUICalling.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCommunicationUICalling/AzureCommunicationUICalling.modulemap";
				PRODUCT_MODULE_NAME = AzureCommunicationUICalling;
				PRODUCT_NAME = AzureCommunicationUICalling;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.8;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E62011F9131F13E0B54C29F1610DF8BE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5633990505F9B3ADD75173ED25153A26 /* Pods-iOSProjectTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProjectTests/Pods-iOSProjectTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProjectTests/Pods-iOSProjectTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E8DE0A01705AF85C560B9264B7755890 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B670DC714CFE9043492068C3CD54B8E /* MicrosoftFluentUI.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MicrosoftFluentUI/MicrosoftFluentUI.modulemap";
				PRODUCT_MODULE_NAME = FluentUI;
				PRODUCT_NAME = FluentUI;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.7;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		EB172EC17EB6676A2D2172C2131CCEA2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 410EC546636B376A22B33FB306BEB539 /* Pods-iOSProject-iOSProjectUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-iOSProject-iOSProjectUITests/Pods-iOSProject-iOSProjectUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-iOSProject-iOSProjectUITests/Pods-iOSProject-iOSProjectUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		ED881CC496C4CD688ECA0F3972394CCF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5FD46B21C237D0FE9D8F321DB739267A /* AzureCommunicationCommon.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon-prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AzureCommunicationCommon/AzureCommunicationCommon.modulemap";
				PRODUCT_MODULE_NAME = AzureCommunicationCommon;
				PRODUCT_NAME = AzureCommunicationCommon;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F4FF6A0D1970CA9705974E3CB2134802 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A5FB2CA797D46CD4DF64D86EEF6CD85 /* Build configuration list for PBXNativeTarget "Pods-iOSProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				91AB63A45CB89E733B62FBCCD40D1F2F /* Debug */,
				99C78A38711FA735E37D8FBF6EE0B0C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2B16BFB70BFB46B09507AE81DDB23F83 /* Build configuration list for PBXNativeTarget "Pods-iOSProject-iOSProjectUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C818A130C776806D0AC80D65691D5C17 /* Debug */,
				EB172EC17EB6676A2D2172C2131CCEA2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3224E19C0C907D2EAEF4027E8BEB151B /* Build configuration list for PBXNativeTarget "MicrosoftFluentUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A076EC0978652AEC2A59B9E996EB3EE /* Debug */,
				E8DE0A01705AF85C560B9264B7755890 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F4FF6A0D1970CA9705974E3CB2134802 /* Debug */,
				30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		54477A465530AC6D511F24067E44E2A0 /* Build configuration list for PBXNativeTarget "AzureCommunicationUICalling" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E1B9D41B97CFD21A71517E7202E8445D /* Debug */,
				2F5B7EF26BCB25F3087B46A9247F4244 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88AA9FD5EAA07074D33B6838B7D0FB07 /* Build configuration list for PBXNativeTarget "AzureCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70564407E7BDDDC9B6D6523F93778966 /* Debug */,
				929DAE3EE6669F1ADEF6C75694227F37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9B8484DC33D295C398D28B22A2965560 /* Build configuration list for PBXAggregateTarget "AzureCommunicationCalling" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95B3979CE997EEF413178F101A0D63C1 /* Debug */,
				32C30850DDF0EC701859ADB707425D43 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D2C9B29A5AF21D5F75E9102B1B003431 /* Build configuration list for PBXNativeTarget "SwipeActions" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C2EAB1D67416083CB17650910787397B /* Debug */,
				AA8FA0A06CEF1C3C2EDCD49C891BBB9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE889601112292FAEA6BDC5B8349F563 /* Build configuration list for PBXNativeTarget "Pods-iOSProjectTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0721EA7255B3D2311E81B9BD26DCB9FD /* Debug */,
				E62011F9131F13E0B54C29F1610DF8BE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F75C89AA415E8A3EE8B274720F139EB2 /* Build configuration list for PBXNativeTarget "AzureCommunicationCommon" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A773A30C590EE479DDBDBA7B1309648 /* Debug */,
				ED881CC496C4CD688ECA0F3972394CCF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC5EBC5D0CF4A28A849A7ADC41504368 /* Build configuration list for PBXNativeTarget "MicrosoftFluentUI-FluentUIResources-ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5E9A37BA873B5E49F3389DBEEBD6E8F3 /* Debug */,
				4E1D40535016BAA382485C412506FADA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
