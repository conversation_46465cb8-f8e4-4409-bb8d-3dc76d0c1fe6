//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Kesken";
"Accessibility.ActivityIndicator.Stopped.label" = "Tilanne pysäytetty";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Hälytys";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Hylkää";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Hylkää kaksoisnapauttamalla";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Valmis";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Vaihda valintaa kaksoisnapauttamalla ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalenteri";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Valitse päivämäärä";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d tapahtumaa";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "ei tapahtumia";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Kuukausi";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Päivä";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Vuosi";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Päivämäärä";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Tunti";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuutti";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AP/IP";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Kuukauden viikko";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Viikonpäivä";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Ensimmäinen";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Toinen";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Kolmas";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Neljäs";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Viimeinen";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Laajenna";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Laajenna kaksoisnapauttamalla";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Kutista";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Kutista kaksoisnapauttamalla";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Valmis";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Epäonnistunut";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Ladataan";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d/%2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Tiliprofiili";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d/%2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Näytä lisää toimintoja kaksoisnapauttamalla";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Lisää toimintoja";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Vaihda asetus kaksoisnapauttamalla";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Käytössä";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Ei käytössä";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ kohdetta";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, lukematon";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Lisää";

/* Generic label for cancel action */
"Common.Cancel" = ".Peruuta";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Jaettu";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Vain minä";

/* Just now date string */
"Date.Now" = "Hetki sitten";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm sitten";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh sitten";

/* Yesterday string */
"Date.Yesterday" = "Eilen";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Eilen kello %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ klo %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Hae hakemistosta";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Alkamisaika";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Päättymisaika";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Alkamispäivä";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Päättymispäivä";

/* Presence - Available status */
"Presence.Available" = "Käytettävissä";

/* Presence - Away status */
"Presence.Away" = "Poissa";

/* Presence - Busy status */
"Presence.Busy" = "Varattu";

/* Presence - Do not disturb status */
"Presence.DND" = "Älä häiritse";

/* Presence - Out of office status */
"Presence.OOF" = "Poissa";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Tuntematon";

/* Presence - Blocked status */
"Presence.Blocked" = "Estetty";
