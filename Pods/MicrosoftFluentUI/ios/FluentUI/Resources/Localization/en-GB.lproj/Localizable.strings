//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "In progress";
"Accessibility.ActivityIndicator.Stopped.label" = "Progress halted";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alert";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Dismiss";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Double tap to dismiss";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Done";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Double tap to toggle selection";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendar";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Select a date";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d events";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "no events";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Month";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Day";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Year";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Date";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hour";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minute";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Week of month";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Day of week";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "First";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Second";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Third";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Quarter";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Last";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Expand";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Double tap to expand";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Collapse";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Double tap to collapse";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Done";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Failed";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Loading";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d of %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Account Profile";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d of %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Double tap to view more actions";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "More actions";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Double tap to toggle setting";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "On";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Off";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ items";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, unread";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "More";

/* Generic label for cancel action */
"Common.Cancel" = "Cancel";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Shared";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Only me";

/* Just now date string */
"Date.Now" = "Just now";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm ago";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh ago";

/* Yesterday string */
"Date.Yesterday" = "Yesterday";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Yesterday at %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ at %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Search Directory";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Start Time";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "End Time";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Start Date";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "End Date";

/* Presence - Available status */
"Presence.Available" = "Available";

/* Presence - Away status */
"Presence.Away" = "Away";

/* Presence - Busy status */
"Presence.Busy" = "Busy";

/* Presence - Do not disturb status */
"Presence.DND" = "Do not disturb";

/* Presence - Out of office status */
"Presence.OOF" = "Out of office";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Unknown";

/* Presence - Blocked status */
"Presence.Blocked" = "Blocked";
