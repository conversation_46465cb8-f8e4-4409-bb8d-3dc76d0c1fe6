//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "בביצוע";
"Accessibility.ActivityIndicator.Stopped.label" = "ההתקדמות הופסקה";

/* Accessibility alert for common use */
"Accessibility.Alert" = "התראה";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "בטל";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "הקש פעמיים כדי לבטל";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "בוצע";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "הקש פעמיים כדי להחליף מצב בחירה";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "לוח שנה";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "בחר תאריך";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d אירועים";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "אין אירועים";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "חודש";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "יום";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "שנה";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "תאריך";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "שעה";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "דקה";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "לפנה\"צ/אחה\"צ";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "שבוע בחודש";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "יום בשבוע";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "ראשון";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "שני";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "שלישי";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "רביעי";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "אחרון";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "הרחב";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "הקש פעמיים כדי להרחיב";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "כווץ";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "הקש פעמיים כדי לכווץ";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "בוצע";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "נכשל";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "טוען";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d מתוך %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "פרופיל חשבון";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d מתוך %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "הקש פעמיים כדי להציג פעולות נוספות";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "פעולות נוספות";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "הקש פעמיים כדי להחליף הגדרה";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "פועל";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "כבוי";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, ‏%@ פריטים";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, לא נקרא";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "עוד";

/* Generic label for cancel action */
"Common.Cancel" = "ביטול";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "משותף";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "אני בלבד";

/* Just now date string */
"Date.Now" = "כרגע";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "לפני %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "לפני %ldh";

/* Yesterday string */
"Date.Yesterday" = "אתמול";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "אתמול ב- %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ ב- %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "חפש במדריך הכתובות";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "שעת התחלה";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "שעת סיום";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "תאריך התחלה";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "תאריך סיום";

/* Presence - Available status */
"Presence.Available" = "זמין/ה";

/* Presence - Away status */
"Presence.Away" = "לא נמצא/ת";

/* Presence - Busy status */
"Presence.Busy" = "עסוק/ה";

/* Presence - Do not disturb status */
"Presence.DND" = "נא לא להפריע";

/* Presence - Out of office status */
"Presence.OOF" = "מחוץ למשרד";

/* Presence - Offline status */
"Presence.Offline" = "במצב לא מקוון";

/* Presence - Unknown status */
"Presence.Unknown" = "לא ידוע";

/* Presence - Blocked status */
"Presence.Blocked" = "חסום/ה";
