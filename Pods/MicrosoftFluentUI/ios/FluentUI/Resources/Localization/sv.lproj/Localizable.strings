//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Pågående";
"Accessibility.ActivityIndicator.Stopped.label" = "<PERSON><PERSON>rlopp har stoppats";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Varning";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Stäng";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dubbeltryck för att stänga";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Klart";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dubbelklicka för att växla markeringen";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalender";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Välj ett datum";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d händelser";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "inga händelser";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Månad";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dag";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "År";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Datum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Timme";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minut";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "FM/EM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Vecka i månaden";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dag i veckan";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Första";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Andra";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Tredje";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Fjärde";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Sista";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Visa";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dubbeltryck för att visa";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Dölj";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dubbeltryck för att dölja";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Klart";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Misslyckades";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Läser in";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d av %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Kontoprofil";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d av %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dubbeltryck för att visa fler åtgärder";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Fler åtgärder";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dubbeltryck för att växla inställning";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "På";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Av";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ objekt";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, oläst";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Mer";

/* Generic label for cancel action */
"Common.Cancel" = "Avbryt";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Delat";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Endast mig";

/* Just now date string */
"Date.Now" = "Just nu";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm sedan";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh sedan";

/* Yesterday string */
"Date.Yesterday" = "I går";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "I går %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ kl. %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Sök i katalog";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Starttid";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Sluttid";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Startdatum";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Slutdatum";

/* Presence - Available status */
"Presence.Available" = "Tillgänglig";

/* Presence - Away status */
"Presence.Away" = "Inte vid datorn";

/* Presence - Busy status */
"Presence.Busy" = "Upptagen";

/* Presence - Do not disturb status */
"Presence.DND" = "Stör ej";

/* Presence - Out of office status */
"Presence.OOF" = "Frånvarande";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Okänd";

/* Presence - Blocked status */
"Presence.Blocked" = "Har blockerats";
