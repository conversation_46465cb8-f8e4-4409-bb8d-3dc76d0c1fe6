//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Probíhá";
"Accessibility.ActivityIndicator.Stopped.label" = "<PERSON>r<PERSON><PERSON>ě<PERSON> zastaven";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Upozornění";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Zavřít";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Poklepáním zavřete.";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Hotovo";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Výběr přepnete dvojitým poklepáním";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalendář";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Vyberte datum";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "počet událostí: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "žádné události";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Měsíc";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Den";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Rok";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Datum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hodina";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuta";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "dop./odp.";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Týden v měsíci";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Den v týdnu";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "1.";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "2.";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "3.";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "4.";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "posled.";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Rozbalit";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Poklepáním rozbalíte";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Sbalit";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dvojitým klepnutím sbalíte";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Hotovo";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Neúspěšné";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Načítání";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d z %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil účtu";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d z %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Poklepáním zobrazíte další akce.";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Další akce";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Poklepáním přepnete nastavení.";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Zapnuto";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Vypnuto";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, počet položek: %@";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, nepřečtené";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Více";

/* Generic label for cancel action */
"Common.Cancel" = "Zrušit";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Sdílené";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Jenom já";

/* Just now date string */
"Date.Now" = "Právě teď";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Před %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Před %ldh";

/* Yesterday string */
"Date.Yesterday" = "Včera";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Včera v %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ v %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Prohledat adresář";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Počáteční čas";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Koncový čas";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Počáteční datum";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Koncové datum";

/* Presence - Available status */
"Presence.Available" = "K dispozici";

/* Presence - Away status */
"Presence.Away" = "Pryč";

/* Presence - Busy status */
"Presence.Busy" = "Nemám čas";

/* Presence - Do not disturb status */
"Presence.DND" = "Nerušit";

/* Presence - Out of office status */
"Presence.OOF" = "Mimo kancelář";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Neznámý";

/* Presence - Blocked status */
"Presence.Blocked" = "Blokováno";
