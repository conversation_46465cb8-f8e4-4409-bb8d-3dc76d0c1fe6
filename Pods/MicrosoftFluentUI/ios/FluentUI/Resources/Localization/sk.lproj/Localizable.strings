//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Prebieha";
"Accessibility.ActivityIndicator.Stopped.label" = "<PERSON>riebeh sa zastavil";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Upozornenie";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Zrušiť";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Zrušíte dvojitým ťuknutím";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Hotovo";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Výber prepnite dvojitým ťuknutím";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalendár";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Vyberte dátum";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "Udalosti: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "žiadne udalosti";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mesiac";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Deň";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Rok";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Dátum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hodina";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minúta";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Týždeň v mesiaci";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Deň v týždni";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "1.";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "2.";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "3.";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "4.";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Posled.";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Rozbaliť";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dvojitým ťuknutím rozbalíte";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Zbaliť";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dvojitým ťuknutím zbalíte";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Hotovo";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Zlyhanie";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Načítava sa";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d z %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil konta";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d z %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dvojitým ťuknutím zobrazíte ďalšie akcie";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Ďalšie akcie";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dvojitým ťuknutím prepnete nastavenie";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Zapnuté";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Vypnuté";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, položky: %@";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, neprečítané";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Viac";

/* Generic label for cancel action */
"Common.Cancel" = "Zrušte ";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Zdieľané";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Iba ja";

/* Just now date string */
"Date.Now" = "Práve teraz";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "pred %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "pred %ldh";

/* Yesterday string */
"Date.Yesterday" = "Včera";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Včera o %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ o %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Prehľadať adresár";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Čas začatia";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Čas ukončenia";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Dátum začatia";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Dátum ukončenia";

/* Presence - Available status */
"Presence.Available" = "K dispozícii";

/* Presence - Away status */
"Presence.Away" = "Som preč";

/* Presence - Busy status */
"Presence.Busy" = "Nemám čas";

/* Presence - Do not disturb status */
"Presence.DND" = "Nerušiť";

/* Presence - Out of office status */
"Presence.OOF" = "Mimo pracoviska";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Neznáme";

/* Presence - Blocked status */
"Presence.Blocked" = "Blokované";
