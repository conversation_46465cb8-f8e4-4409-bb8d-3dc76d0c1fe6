PODS:
  - AzureCommunicationCalling (2.15.0):
    - AzureCommunicationCommon (~> 1.0)
  - AzureCommunicationCommon (1.3.0)
  - AzureCommunicationUICalling (1.14.2):
    - AzureCommunicationCalling (= 2.15.0)
    - AzureCore (= 1.0.0-beta.16)
    - MicrosoftFluentUI/ActivityIndicator_ios (= 0.10.0)
    - MicrosoftFluentUI/Avatar_ios (= 0.10.0)
    - MicrosoftFluentUI/AvatarGroup_ios (= 0.10.0)
    - MicrosoftFluentUI/BottomSheet_ios (= 0.10.0)
    - MicrosoftFluentUI/Button_ios (= 0.10.0)
    - MicrosoftFluentUI/PopupMenu_ios (= 0.10.0)
  - AzureCore (1.0.0-beta.16)
  - MicrosoftFluentUI/ActivityIndicator_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/Avatar_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/AvatarGroup_ios (0.10.0):
    - MicrosoftFluentUI/Avatar_ios
  - MicrosoftFluentUI/BottomSheet_ios (0.10.0):
    - MicrosoftFluentUI/Obscurable_ios
    - MicrosoftFluentUI/ResizingHandleView_ios
  - MicrosoftFluentUI/Button_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/Core_ios (0.10.0)
  - MicrosoftFluentUI/Drawer_ios (0.10.0):
    - MicrosoftFluentUI/Obscurable_ios
    - MicrosoftFluentUI/ResizingHandleView_ios
    - MicrosoftFluentUI/Separator_ios
    - MicrosoftFluentUI/TouchForwardingView_ios
  - MicrosoftFluentUI/Label_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/Obscurable_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/PopupMenu_ios (0.10.0):
    - MicrosoftFluentUI/Drawer_ios
    - MicrosoftFluentUI/Label_ios
    - MicrosoftFluentUI/Separator_ios
    - MicrosoftFluentUI/TableView_ios
  - MicrosoftFluentUI/ResizingHandleView_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/Separator_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - MicrosoftFluentUI/TableView_ios (0.10.0):
    - MicrosoftFluentUI/Label_ios
    - MicrosoftFluentUI/Separator_ios
  - MicrosoftFluentUI/TouchForwardingView_ios (0.10.0):
    - MicrosoftFluentUI/Core_ios
  - SwipeActions (0.3.3)

DEPENDENCIES:
  - AzureCommunicationUICalling
  - SwipeActions

SPEC REPOS:
  trunk:
    - AzureCommunicationCalling
    - AzureCommunicationCommon
    - AzureCommunicationUICalling
    - AzureCore
    - MicrosoftFluentUI
    - SwipeActions

SPEC CHECKSUMS:
  AzureCommunicationCalling: 96df4da223a0faa2926ff7a99c6960ee1eec14a3
  AzureCommunicationCommon: 945d9b93a4b52f64e8aacdc992fc8d4a80973d9f
  AzureCommunicationUICalling: 98c5473bf94042acc26d684a6ded09fb297187f8
  AzureCore: 1e940658808f94fdf6a23264febfc516fb59fe3e
  MicrosoftFluentUI: f6db695718efb93f4ca9bdc366c00b80a1f91dba
  SwipeActions: 3c35871b408cab23ea67f3946a650356795250d2

PODFILE CHECKSUM: e018031f7e8027ac813a99e068c3f4343e4a45b6

COCOAPODS: 1.16.2
