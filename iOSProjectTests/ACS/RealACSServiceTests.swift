//
//  RealACSServiceTests.swift
//  iOSProjectTests
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import XCTest
@testable import iOSProject

// MARK: - Real ACS Service Tests

@MainActor
final class RealACSServiceTests: XCTestCase {
    
    // MARK: - Properties
    
    var sut: ACSService!
    var tokenProvider: ACSTokenProvider!
    var configuration: ACSConfiguration!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        configuration = ACSConfiguration.default
        tokenProvider = ACSTokenProvider(configuration: configuration)
        sut = ACSService(configuration: configuration, tokenProvider: tokenProvider)
    }
    
    override func tearDown() {
        sut = nil
        tokenProvider = nil
        configuration = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testRealACSService_Initialization() {
        // Given & When
        let service = ACSService(configuration: configuration, tokenProvider: tokenProvider)
        
        // Then
        XCTAssertNotNil(service)
        XCTAssertEqual(service.callState, .idle)
    }
    
    // MARK: - Permission Tests
    
    func testCheckPermissions_RealImplementation() async {
        // When
        let hasPermissions = await sut.checkPermissions()
        
        // Then
        // This will depend on the actual device permissions
        // In a test environment, permissions might not be granted
        XCTAssertNotNil(hasPermissions)
    }
    
    // MARK: - Token Provider Integration Tests
    
    func testTokenProvider_RealImplementation() async throws {
        // When
        let token = try await tokenProvider.fetchToken()
        
        // Then
        XCTAssertFalse(token.isEmpty)
        XCTAssertTrue(token.contains("."), "Token should be in JWT format")
    }
    
    func testTokenProvider_Caching() async throws {
        // When
        let firstToken = try await tokenProvider.fetchToken()
        let secondToken = try await tokenProvider.refreshTokenIfNeeded()
        
        // Then
        XCTAssertEqual(firstToken, secondToken, "Token should be cached")
    }
    
    // MARK: - Configuration Tests
    
    func testACSConfiguration_RealValues() {
        // Given
        let config = ACSConfiguration.default
        
        // Then
        XCTAssertFalse(config.connectionString.isEmpty)
        XCTAssertFalse(config.defaultDisplayName.isEmpty)
        XCTAssertGreaterThan(config.callTimeout, 0)
    }
    
    // MARK: - Call State Tests
    
    func testCallState_InitialState() {
        // Given & When
        let initialState = sut.callState
        
        // Then
        XCTAssertEqual(initialState, .idle)
    }
    
    // MARK: - Error Handling Tests
    
    func testACSService_ErrorHandling() async {
        // Given
        // We'll test error handling by trying to create a call without proper token
        
        // When & Then
        do {
            // This might fail due to token issues, which is expected in test environment
            _ = try await sut.createGroupCall(displayName: "Test User")
            // If it succeeds, that's also fine - it means the service is working
        } catch {
            // Verify that we get proper ACS errors
            XCTAssertTrue(error is ACSError || error.localizedDescription.contains("token"))
        }
    }
    
    // MARK: - Integration with DI Container Tests
    
    func testDependencyInjection_RealService() {
        // Given
        let container = DependencyContainer()
        
        // When
        let acsService = container.acsService
        
        // Then
        XCTAssertTrue(acsService is ACSService, "Should resolve to real ACS service")
        XCTAssertEqual(acsService.callState, .idle)
    }
    
    // MARK: - Performance Tests
    
    func testACSService_CreationPerformance() {
        measure {
            let _ = ACSService(configuration: configuration, tokenProvider: tokenProvider)
        }
    }
    
    func testTokenGeneration_Performance() {
        measure {
            Task {
                do {
                    _ = try await tokenProvider.fetchToken()
                } catch {
                    // Performance test - we don't care about the result, just the time
                }
            }
        }
    }
    
    // MARK: - Azure SDK Availability Tests
    
    func testAzureSDK_Availability() {
        // Given & When
        let isUICallingAvailable = AzureSDKAvailability.isUICallingAvailable
        let isCallingAvailable = AzureSDKAvailability.isCallingAvailable
        let isCommonAvailable = AzureSDKAvailability.isCommonAvailable
        let isFullyAvailable = AzureSDKAvailability.isFullyAvailable
        
        // Then
        XCTAssertTrue(isUICallingAvailable, "UI Calling should be available")
        XCTAssertTrue(isCallingAvailable, "Calling should be available")
        XCTAssertTrue(isCommonAvailable, "Common should be available")
        XCTAssertTrue(isFullyAvailable, "Full SDK should be available")
    }
    
    // MARK: - Real vs Mock Service Tests
    
    func testServiceType_IsRealACSService() {
        // Given
        let container = DependencyContainer()
        let service = container.acsService
        
        // When & Then
        XCTAssertTrue(service is ACSService, "Should be real ACS service, not mock")
        XCTAssertFalse(service is MockACSService, "Should not be mock service")
    }
}
